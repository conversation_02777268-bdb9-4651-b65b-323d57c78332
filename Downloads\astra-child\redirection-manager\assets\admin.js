jQuery(document).ready(function($) {
    'use strict';

    // Elementos do DOM
    const $saveBtn = $('#rm-save-settings');
    const $resetBtn = $('#rm-reset-settings');
    const $addExceptionBtn = $('#rm-add-exception-btn');
    const $newExceptionInput = $('#rm-new-exception');
    const $exceptionsList = $('.rm-exceptions-list');
    const $loading = $('#rm-loading');


    // Inicialização
    init();

    function init() {
        bindEvents();
        setupTooltips();
    }

    function bindEvents() {
        // Salvar configurações
        $saveBtn.on('click', saveSettings);
        
        // Resetar configurações
        $resetBtn.on('click', resetSettings);
        
        // Adicionar exceção
        $addExceptionBtn.on('click', addException);
        $newExceptionInput.on('keypress', function(e) {
            if (e.which === 13) {
                addException();
            }
        });
        
        // Remover exceção
        $(document).on('click', '.rm-remove-exception', removeException);
        
        // Toggle de status
        $('#rm-enabled').on('change', function() {
            const isEnabled = $(this).is(':checked');
            toggleSystemStatus(isEnabled);
        });
    }

    function setupTooltips() {
        // Adicionar tooltips para melhor UX
        $('[data-tooltip]').each(function() {
            $(this).attr('title', $(this).data('tooltip'));
        });
    }

    function saveSettings() {
        showLoading(true);
        
        const settings = {
            action: 'save_redirection_settings',
            nonce: rm_ajax.nonce,
            enabled: $('#rm-enabled').is(':checked'),
            login_page: $('#rm-login-page').val().trim()
        };

        // Validação básica
        if (!settings.login_page) {
            alert('Por favor, preencha o campo da página de login.');
            showLoading(false);
            return;
        }

        $.ajax({
            url: rm_ajax.url,
            type: 'POST',
            data: settings,
            success: function(response) {
                if (!response.success) {
                    alert('Erro ao salvar configurações: ' + response.data);
                }
            },
            error: function() {
                alert('Erro de conexão. Tente novamente.');
            },
            complete: function() {
                showLoading(false);
            }
        });
    }

    function resetSettings() {
        if (!confirm('Tem certeza que deseja restaurar as configurações padrão? Esta ação não pode ser desfeita.')) {
            return;
        }

        showLoading(true);
        
        // Restaurar valores padrão
        $('#rm-enabled').prop('checked', true);
        $('#rm-login-page').val('acessar');
        
        // Salvar automaticamente
        setTimeout(saveSettings, 500);
    }

    function addException() {
        const page = $newExceptionInput.val().trim();
        
        if (!page) {
            alert('Digite o nome da página para adicionar à lista de exceções.');
            return;
        }

        // Verificar se já existe
        if ($('.rm-exception-item[data-page="' + page + '"]').length > 0) {
            alert('Esta página já está na lista de exceções.');
            return;
        }

        $.ajax({
            url: rm_ajax.url,
            type: 'POST',
            data: {
                action: 'add_exception',
                nonce: rm_ajax.nonce,
                page: page
            },
            success: function(response) {
                if (response.success) {
                    addExceptionToList(page);
                    $newExceptionInput.val('');
                } else {
                    alert('Erro ao adicionar exceção: ' + response.data);
                }
            },
            error: function() {
                alert('Erro de conexão. Tente novamente.');
            }
        });
    }

    function removeException() {
        const page = $(this).data('page');
        const $item = $(this).closest('.rm-exception-item');
        
        if (!confirm('Remover "' + page + '" da lista de exceções?')) {
            return;
        }

        $.ajax({
            url: rm_ajax.url,
            type: 'POST',
            data: {
                action: 'remove_exception',
                nonce: rm_ajax.nonce,
                page: page
            },
            success: function(response) {
                if (response.success) {
                    $item.fadeOut(300, function() {
                        $(this).remove();
                    });
                } else {
                    alert('Erro ao remover exceção: ' + response.data);
                }
            },
            error: function() {
                alert('Erro de conexão. Tente novamente.');
            }
        });
    }

    function addExceptionToList(page) {
        const $item = $(`
            <div class="rm-exception-item" data-page="${page}" style="display: none;">
                <span class="rm-exception-name">${page}</span>
                <button type="button" class="rm-remove-exception" data-page="${page}">
                    <i class="dashicons dashicons-no-alt"></i>
                </button>
            </div>
        `);
        
        $exceptionsList.append($item);
        $item.fadeIn(300);
    }

    function toggleSystemStatus(enabled) {
        const $cards = $('.rm-card').not('.rm-status-card');
        
        if (enabled) {
            $cards.removeClass('rm-disabled');
        } else {
            $cards.addClass('rm-disabled');
        }
    }



    function showLoading(show) {
        if (show) {
            $loading.fadeIn(200);
        } else {
            $loading.fadeOut(200);
        }
    }



    // Validação em tempo real
    $('input[type="text"]').on('input', function() {
        const $field = $(this);
        const value = $field.val().trim();
        
        // Remover caracteres especiais para slugs
        if ($field.attr('id') !== 'rm-settings-page') {
            const cleanValue = value.replace(/[^a-z0-9-]/gi, '').toLowerCase();
            if (cleanValue !== value) {
                $field.val(cleanValue);
            }
        }
        
        // Validação visual
        if (value.length > 0) {
            $field.removeClass('rm-field-error').addClass('rm-field-success');
        } else {
            $field.removeClass('rm-field-success rm-field-error');
        }
    });

    // Adicionar classes CSS para validação
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .rm-field-success {
                border-color: #28a745 !important;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
            }
            .rm-field-error {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
            }
            .rm-disabled {
                opacity: 0.6;
                pointer-events: none;
            }
            .rm-notification.warning {
                border-left-color: #ffc107;
            }
        `)
        .appendTo('head');

    // Atalhos de teclado
    $(document).on('keydown', function(e) {
        // Ctrl/Cmd + S para salvar
        if ((e.ctrlKey || e.metaKey) && e.which === 83) {
            e.preventDefault();
            saveSettings();
        }
        
        // Esc para fechar loading
        if (e.which === 27) {
            showLoading(false);
        }
    });

    // Animações de entrada
    $('.rm-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });
});