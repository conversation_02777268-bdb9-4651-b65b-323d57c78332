/* Base Container Styles */
.custom-carousel-wrapper {
    position: relative !important;
    width: 100%;
    overflow: hidden !important;
    z-index: 9;
    margin-bottom: 30px;
}

.custom-carousel-wrapper .swiper {
    position: relative;
    width: 100%;
    overflow: visible;
}

/* Wrapper and Slide Structure */
.swiper-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    box-sizing: content-box;
}

.swiper-slide {
    display: flex;
    justify-content: flex-start;
    align-items: stretch; /* Mudado para stretch */
    width: auto;
    height: auto; /* Mudado para auto */
    position: relative;
    transition-property: transform;
}

/* Carousel Item Styles */
.carousel-item {
    position: relative !important;
    width: 100%;
    min-height: 100px; /* Altura m���nima para evitar colapso */
    border-radius: 10px;
    overflow: hidden;
    z-index: 9;
    display: flex; /* Adicionado */
}

/* Link and Image Container */
.carousel-item a {
    position: relative; /* Mudado para relative */
    width: 100%;
    display: flex; /* Adicionado */
    flex-direction: column; /* Adicionado */
}

/* Image Styles */
.carousel-item img {
    width: 100%;
    height: auto;
    display: block;
    min-height: 100%; /* Garante altura m���nima */
    object-fit: contain; /* Mudado para contain */
    transition: transform 0.3s ease, filter 0.3s ease;
}



/* Navigation Arrows */
.swiper-button-next,
.swiper-button-prev {
    color: #fff !important;
    background-color: rgba(0, 0, 0, 0.5);
    width: 60px !important;
    height: 60px !important;
    border-radius: 50%;
    z-index: 20;
    transition: all 0.3s ease;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 20px !important;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background-color: rgba(255, 255, 255, 0.3);
    color: #000 !important;
}

/* Multiple Carousels Handling */
.elementor-widget-custom_carousel {
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
}

.elementor-widget-custom_carousel + .elementor-widget-custom_carousel {
    margin-top: 50px;
}

/* Utility Classes */
.hide-comments {
    display: none !important;
}

.carousel-item-message {
    text-align: center !important;
}

/* Efeitos */
.carousel-item:hover img {
    filter: brightness(0.8);
    transform: scale(1.05);
}

.carousel-item img.grayscale {
    filter: grayscale(100%);
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .swiper-slide {
        justify-content: center;
    }

    .carousel-item {
        min-height: 80px; /* Altura m���nima menor para mobile */
    }

    .swiper-button-next,
    .swiper-button-prev {
        width: 35px !important;
        height: 35px !important;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 16px !important;
    }

}

/* Hide Scrollbar */
.swiper::-webkit-scrollbar {
    display: none;
}