<?php
/**
 * Script de migração para transferir configurações do functions.php para o plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class RedirectionMigration {
    
    public static function migrate_settings() {
        // Mapear configurações antigas para novas
        $old_to_new_mapping = [
            'redirection_enabled' => 'rm_redirection_enabled',
            'redirection_login_page' => 'rm_login_page',
            'redirection_register_page' => 'rm_register_page',
            'redirection_member_area' => 'rm_member_area'
        ];
        
        $migrated = false;
        
        foreach ($old_to_new_mapping as $old_key => $new_key) {
            $old_value = get_option($old_key);
            
            if ($old_value !== false) {
                // Transferir valor para nova opção
                update_option($new_key, $old_value);
                $migrated = true;
            }
        }
        
        // Configurações específicas do plugin
        if ($migrated) {
            // Definir configurações padrão específicas do plugin
            update_option('rm_settings_page', 'painel/settings/');
            update_option('rm_exceptions', ['cursos']);
            update_option('rm_admin_redirections', [
                'painel' => 'painel/settings/',
                'courses' => get_option('rm_member_area', 'area-de-membros')
            ]);
            
            // Marcar migração como concluída
            update_option('rm_migration_completed', true);
            
            return true;
        }
        
        return false;
    }
    
    public static function cleanup_old_settings() {
        // Remover configurações antigas após migração bem-sucedida
        $old_options = [
            'redirection_enabled',
            'redirection_login_page', 
            'redirection_register_page',
            'redirection_member_area'
        ];
        
        foreach ($old_options as $option) {
            delete_option($option);
        }
    }
    
    public static function is_migration_needed() {
        return !get_option('rm_migration_completed', false) && 
               get_option('redirection_enabled') !== false;
    }
}

// Executar migração automaticamente na ativação do plugin
register_activation_hook(__FILE__, function() {
    if (RedirectionMigration::is_migration_needed()) {
        if (RedirectionMigration::migrate_settings()) {
            // Opcional: limpar configurações antigas
            // RedirectionMigration::cleanup_old_settings();
        }
    }
});