<?php
/**
 * Custom User Courses Management
 *
 * This file handles custom user course management, including enrollment,
 * email notifications, and webhook integration.
 *
 * @package YourTheme
 * @subpackage UserManagement
 */

// Exit if accessed directly.
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Global Webhook URL Management
 */
function get_global_webhook_url() {
    return get_option('global_webhook_url', '');
}

function update_global_webhook_url($url) {
    update_option('global_webhook_url', esc_url_raw($url));
}

/**
 * Generate webhook data for a user.
 *
 * @param WP_User|array $user User object or array of user data.
 * @return array Webhook data.
 */
function generate_webhook_data($user) {
    if (is_array($user)) {
        $user_id = isset($user['ID']) ? $user['ID'] : null;
        $data = array(
            'email' => isset($user['email']) ? $user['email'] : 'N/A',
            'username' => isset($user['username']) ? $user['username'] : 'N/A',
            'first_name' => isset($user['first_name']) ? $user['first_name'] : 'N/A',
            'display_name' => isset($user['display_name']) ? $user['display_name'] : 'N/A',
            'whatsapp' => $user_id ? get_user_whatsapp($user_id) : 'N/A'
        );
    } elseif (is_object($user) && is_a($user, 'WP_User')) {
        $data = array(
            'email' => $user->user_email,
            'username' => $user->user_login,
            'first_name' => $user->first_name,
            'display_name' => $user->display_name,
            'whatsapp' => get_user_whatsapp($user->ID)
        );
    } else {
        return array();
    }

    return $data;
}


/**
 * User Interface Functions
 */
function custom_user_courses_field($user) {
    $courses = get_posts(array('post_type' => 'courses', 'numberposts' => -1));
    $tutor_courses = tutor_utils()->get_enrolled_courses_ids_by_user($user->ID);
    $user_courses = get_user_meta($user->ID, '_user_courses', true);
    $user_courses = is_array($user_courses) ? $user_courses : array();
    $all_courses = array_unique(array_merge($tutor_courses, $user_courses));

    wp_nonce_field('save_custom_user_courses', '_custom_user_courses_nonce');

    $site_title = get_bloginfo('name');
    ?>
    <h3><?php printf(__('Cursos %s', 'your-text-domain'), $site_title); ?></h3>
    <div class="courses-grid">
        <?php foreach ($courses as $course) : ?>
            <div class="course-item">
                <label>
                    <input type="checkbox" name="custom_user_courses[]" 
                           value="<?php echo esc_attr($course->ID); ?>" 
                           <?php checked(in_array($course->ID, $all_courses)); ?>>
                    <?php echo esc_html($course->post_title); ?>
                </label>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
    custom_user_email_settings($user);
}

function custom_user_courses_field_new_user() {
    $courses = get_posts(array('post_type' => 'courses', 'numberposts' => -1));
    wp_nonce_field('save_custom_user_courses', '_custom_user_courses_nonce');

    $site_title = get_bloginfo('name');
    ?>
    <h3><?php printf(__('Cursos %s', 'your-text-domain'), $site_title); ?></h3>
    <div class="courses-grid">
        <?php foreach ($courses as $course) : ?>
            <div class="course-item">
                <label>
                    <input type="checkbox" name="custom_user_courses[]" value="<?php echo esc_attr($course->ID); ?>">
                    <?php echo esc_html($course->post_title); ?>
                </label>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
    custom_user_email_settings();
}


function custom_user_email_settings($user = null) {
    $send_email = $user ? get_user_meta($user->ID, '_send_access_email', true) : 0;
    $webhook_url = get_global_webhook_url();
    ?>
    <h3 style="margin-top: 3%;"><?php _e('Configurações de E-mail de Acesso', 'your-text-domain'); ?></h3>
    <table class="form-table ucc-profile-edit-form">
        <tr>
            <th><label for="send_access_email"><?php _e('Enviar E-mail de Acesso', 'your-text-domain'); ?></label></th>
            <td>
                <label class="ucc-switch">
                    <input type="checkbox" id="send_access_email" name="send_access_email" value="1" <?php checked($send_email, 1); ?>>
                    <span class="ucc-slider ucc-round"></span>
                </label>
            </td>
        </tr>
        <?php if (current_user_can('manage_options')): ?>
        <tr>
            <th><label for="webhook_url"><?php _e('URL do Webhook Global', 'your-text-domain'); ?></label></th>
            <td>
                <input type="url" id="webhook_url" name="webhook_url" value="<?php echo esc_attr($webhook_url); ?>" class="regular-text">
                <p class="description"><?php _e('Insira a URL do webhook global para envio dos dados de acesso.', 'your-text-domain'); ?></p>
            </td>
        </tr>
        <?php endif; ?>
    </table>
    <?php if (current_user_can('manage_options')): ?>
    <p style="margin-bottom: 3%;">
        <button type="button" id="test_webhook" class="button"><?php _e('Testar Envio', 'your-text-domain'); ?></button>
        <span id="webhook_test_result"></span>
    </p>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#test_webhook').on('click', function() {
            var webhookUrl = $('#webhook_url').val();
            var testData = {
                action: 'test_webhook',
                webhook_url: webhookUrl,
                nonce: '<?php echo wp_create_nonce('test_webhook_nonce'); ?>'
            };

            $.post(ajaxurl, testData, function(response) {
                $('#webhook_test_result').text(response.data);
            });
        });
    });
    </script>
    <?php endif;
}

/**
 * Styling
 */
function custom_user_courses_css() {
    ?>
    <style>
        .ucc-profile-edit-form .ucc-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .ucc-profile-edit-form .ucc-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .ucc-profile-edit-form .ucc-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }
        .ucc-profile-edit-form .ucc-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }
        .ucc-profile-edit-form .ucc-switch input:checked + .ucc-slider {
            background-color: #2196F3;
        }
        .ucc-profile-edit-form .ucc-switch input:checked + .ucc-slider:before {
            transform: translateX(26px);
        }
        .ucc-profile-edit-form .ucc-slider.ucc-round {
            border-radius: 34px;
        }
        .ucc-profile-edit-form .ucc-slider.ucc-round:before {
            border-radius: 50%;
        }
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 10px;
        }
        .course-item {
            background: #f9f9f9;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
    </style>
    <?php
}

/**
 * Save custom user courses and settings.
 *
 * @param int $user_id User ID.
 * @return bool|void
 */
function custom_save_user_courses($user_id) {
    if (!current_user_can('edit_user', $user_id) || !check_admin_referer('save_custom_user_courses', '_custom_user_courses_nonce')) {
        return false;
    }

    $new_courses = isset($_POST['custom_user_courses']) ? array_map('intval', $_POST['custom_user_courses']) : array();
    $current_courses = get_user_meta($user_id, '_user_courses', true);
    $current_courses = is_array($current_courses) ? $current_courses : array();

    $courses_changed = ($new_courses !== $current_courses);

    if ($courses_changed) {
        update_user_enrollments($user_id, $new_courses, $current_courses);
        update_user_meta($user_id, '_user_courses', $new_courses);
        wp_cache_delete('user_' . $user_id . '_courses');
    }

    $new_send_email = isset($_POST['send_access_email']) ? 1 : 0;
    $current_send_email = get_user_meta($user_id, '_send_access_email', true);

    $email_settings_changed = ($new_send_email != $current_send_email);

    update_user_meta($user_id, '_send_access_email', $new_send_email);

    // Update global webhook URL if user has permissions
    $webhook_url_changed = false;
    if (current_user_can('manage_options') && isset($_POST['webhook_url'])) {
        $new_webhook_url = esc_url_raw($_POST['webhook_url']);
        $current_webhook_url = get_global_webhook_url();
        if ($new_webhook_url !== $current_webhook_url) {
            update_global_webhook_url($new_webhook_url);
            $webhook_url_changed = true;
        }
    }

    $webhook_url = get_global_webhook_url();

    // Set a flag to send webhook later
    if (($courses_changed || $email_settings_changed || $webhook_url_changed) && 
        $new_send_email && 
        !empty($webhook_url) && 
        wp_get_environment_type() === 'production') {
        update_user_meta($user_id, '_pending_webhook', '1');
    }
}

/**
 * Send webhook data after user is completely saved.
 *
 * @param int $user_id User ID.
 */
/**
 * Send webhook data after user is completely saved.
 *
 * @param int $user_id User ID.
 */
function send_webhook_after_user_save($user_id) {
    $last_webhook_time = get_user_meta($user_id, '_last_webhook_time', true);
    $current_time = time();

    $webhook_url = get_global_webhook_url();
    $send_email = get_user_meta($user_id, '_send_access_email', true);

    if ($send_email && !empty($webhook_url)) {
        $user = get_userdata($user_id);
        if ($user) {
            $data = generate_webhook_data($user);

            // Send the webhook only if it hasn't been sent in the last 5 seconds
            if (!$last_webhook_time || ($current_time - $last_webhook_time) >= 5) {
                $response = wp_remote_post($webhook_url, array(
                    'body' => json_encode($data),
                    'headers' => array('Content-Type' => 'application/json'),
                ));

                if (!is_wp_error($response)) {
                    $response_code = wp_remote_retrieve_response_code($response);
                    
                    if ($response_code === 200) {
                        update_user_meta($user_id, '_last_webhook_time', $current_time);
                    }
                }
            }
        }
    }
}



function update_user_enrollments($user_id, $new_courses, $current_courses) {
    global $wpdb;

    if (function_exists('tutils')) {
        foreach ($new_courses as $course_id) {
            if (!in_array($course_id, $current_courses)) {
                tutils()->do_enroll($course_id, 0, $user_id);
            }
        }

        foreach ($current_courses as $course_id) {
            if (!in_array($course_id, $new_courses)) {
                $enrollment_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT ID FROM {$wpdb->posts} 
                    WHERE post_type = 'tutor_enrolled' 
                    AND post_parent = %d 
                    AND post_author = %d",
                    $course_id,
                    $user_id
                ));

                if ($enrollment_id) {
                    wp_delete_post($enrollment_id, true);
                }
            }
        }
    }
}


/**
 * User List Customization
 */
function custom_user_courses_column($columns) {
    $columns['user_courses'] = __('Cursos', 'your-text-domain');
    return $columns;
}

function custom_user_courses_column_content($value, $column_name, $user_id) {
    if ('user_courses' == $column_name) {
        $cache_key = 'user_' . $user_id . '_courses';
        $all_courses = wp_cache_get($cache_key);

        if (false === $all_courses) {
            $enrolled_courses = tutor_utils()->get_enrolled_courses_ids_by_user($user_id);
            $meta_courses = get_user_meta($user_id, '_user_courses', true);
            $meta_courses = is_array($meta_courses) ? $meta_courses : array();
            $all_courses = array_unique(array_merge($enrolled_courses, $meta_courses));
            wp_cache_set($cache_key, $all_courses, '', 12 * HOUR_IN_SECONDS);
        }

        if (!empty($all_courses)) {
            $course_titles = array_map(function($course_id) {
                $course = get_post($course_id);
                return $course ? $course->post_title : '';
            }, $all_courses);
            $value = implode(', ', array_filter($course_titles));
        } else {
            $value = __('Nenhum', 'your-text-domain');
        }
    }
    return $value;
}

/**
 * Test webhook callback with fictional data.
 */
function test_webhook_callback() {
    check_ajax_referer('test_webhook_nonce', 'nonce');

    $webhook_url = isset($_POST['webhook_url']) ? esc_url_raw($_POST['webhook_url']) : '';

    if (empty($webhook_url)) {
        wp_send_json_error(__('URL do webhook inválida.', 'your-text-domain'));
    }

    // Dados fictícios para teste
    $test_user = array(
        'ID' => 0, // ID fictício
        'email' => '<EMAIL>',
        'username' => 'usuario.teste',
        'first_name' => 'Usuário',
        'display_name' => 'Usuário Teste',
    );

    $data = generate_webhook_data($test_user);

    // Adiciona o WhatsApp fictício para o teste
    $data['whatsapp'] = '+55 11 98765-4321';

    $response = wp_remote_post($webhook_url, array(
        'body' => json_encode($data),
        'headers' => array('Content-Type' => 'application/json'),
    ));

    if (is_wp_error($response)) {
        wp_send_json_error($response->get_error_message());
    } else {
        wp_send_json_success(__('Teste enviado com sucesso! Dados fictícios foram usados.', 'your-text-domain'));
    }
}

/**
 * Hook functions
 */
add_action('show_user_profile', 'custom_user_courses_field');
add_action('edit_user_profile', 'custom_user_courses_field');
add_action('user_new_form', 'custom_user_courses_field_new_user');
add_action('admin_head', 'custom_user_courses_css');
add_action('personal_options_update', 'custom_save_user_courses');
add_action('edit_user_profile_update', 'custom_save_user_courses');
add_action('user_register', 'custom_save_user_courses');
add_filter('manage_users_columns', 'custom_user_courses_column');
add_filter('manage_users_custom_column', 'custom_user_courses_column_content', 10, 3);
add_action('wp_ajax_test_webhook', 'test_webhook_callback');
add_action('profile_update', 'send_webhook_after_user_save', 20);
add_action('user_register', 'send_webhook_after_user_save', 20);