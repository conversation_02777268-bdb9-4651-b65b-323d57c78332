# Custom New User Email Plugin

Plugin personalizado para customizar o email de criação de novo usuário do WordPress com design white label e templates de email modernos.

## 📋 Descrição

Este plugin permite personalizar completamente o email que é enviado quando um novo usuário é criado no WordPress. Ele oferece um template moderno, responsivo e totalmente customizável para melhorar a experiência do usuário.

## ✨ Características

- **Template Moderno**: Design clean e profissional
- **Totalmente Responsivo**: Funciona perfeitamente em dispositivos móveis
- **Customização Completa**: Cores, textos, padrões e layout personalizáveis
- **Padrões de Cabeçalho**: 8 padrões diferentes para o cabeçalho do email
- **Preview em Tempo Real**: Visualize as mudanças antes de salvar
- **Email de Teste**: Envie emails de teste para verificar a aparência
- **Dados de Acesso**: Exibe username, senha e link de login de forma organizada
- **Nota de Segurança**: Inclui recomendações de segurança para o usuário

## 🚀 Instalação

1. Faça upload da pasta `custom-new-user-email` para o diretório `/wp-content/plugins/`
2. Ative o plugin através do menu 'Plugins' no WordPress
3. Vá para 'Configurações' > 'Email Novo Usuário' para configurar

## ⚙️ Configuração

### Configurações de Email

- **Habilitar Email Personalizado**: Ativa/desativa o template personalizado
- **Assunto do Email**: Personaliza o assunto do email
- **Nome e Email do Remetente**: Define quem aparece como remetente
- **Cores**: Personalize cores do cabeçalho e botões
- **Padrões de Cabeçalho**: Escolha entre 8 padrões diferentes
- **Textos**: Customize todas as mensagens do email
- **Labels**: Personalize os rótulos dos dados de acesso

### Padrões de Cabeçalho Disponíveis

1. **Nenhum**: Cabeçalho sólido sem padrões
2. **Pontos Flutuantes**: Pontos espalhados aleatoriamente
3. **Formas Orgânicas**: Formas abstratas suaves
4. **Geométrico Minimal**: Padrões geométricos simples
5. **Linhas Fluidas**: Linhas curvas e fluidas
6. **Elementos Espalhados**: Mistura de pontos e linhas
7. **Grade Moderna**: Grade com elementos de destaque
8. **Arte Abstrata**: Combinação artística de formas

## 🎨 Personalização

### Cores Personalizáveis

- Cor do cabeçalho
- Cor dos botões
- Padrões automáticos baseados na cor principal

### Textos Personalizáveis

- Saudação
- Texto principal
- Título das credenciais
- Labels dos dados de acesso
- Texto de instrução
- Texto do botão
- Nota de segurança
- Texto do rodapé

## 📧 Template de Email

O template inclui:

- **Cabeçalho**: Com título e padrão personalizado
- **Saudação**: Personalizada com nome do usuário
- **Mensagem Principal**: Texto de boas-vindas
- **Seção de Credenciais**: 
  - Nome de usuário
  - Senha
  - Link de acesso
- **Botão de Ação**: Link direto para login
- **Nota de Segurança**: Recomendações de segurança
- **Rodapé**: Informações adicionais

## 🔧 Hooks Utilizados

- `wp_new_user_notification_email`: Personaliza o email de novo usuário
- `wp_mail_content_type`: Define o tipo de conteúdo como HTML
- `wp_mail_from`: Personaliza o email do remetente
- `wp_mail_from_name`: Personaliza o nome do remetente

## 📱 Responsividade

O template é totalmente responsivo e se adapta a:

- Desktops
- Tablets
- Smartphones
- Clientes de email diversos

## 🧪 Teste e Preview

- **Preview em Tempo Real**: Visualize mudanças instantaneamente
- **Email de Teste**: Envie emails de teste para qualquer endereço
- **Dados Fictícios**: Preview usa dados de exemplo realistas

## 🔒 Segurança

- Sanitização de todos os inputs
- Verificação de permissões
- Nonces para proteção CSRF
- Validação de emails

## 📋 Requisitos

- WordPress 5.0 ou superior
- PHP 7.4 ou superior
- Extensões PHP: json, mbstring

## 🤝 Compatibilidade

- Funciona com qualquer tema WordPress
- Compatible com plugins de cache populares
- Suporte a multisite

## 📝 Changelog

### v1.0.0
- Lançamento inicial
- Template de email personalizado
- Painel de administração completo
- Sistema de preview e teste
- 8 padrões de cabeçalho
- Responsividade completa

## 🆘 Suporte

Para suporte e dúvidas, entre em contato através do painel administrativo do WordPress.

## 📄 Licença

Este plugin é licenciado sob a GPL v2 ou posterior.

---

**Desenvolvido para Astra Child Theme**
