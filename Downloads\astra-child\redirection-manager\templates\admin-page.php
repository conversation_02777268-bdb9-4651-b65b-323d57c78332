<div class="wrap rm-admin-wrap">
    <div class="rm-header">
        <h1><i class="dashicons dashicons-admin-links"></i> Redirection Manager</h1>
        <p class="rm-subtitle">Gerencie todos os redirecionamentos do seu site de forma centralizada</p>
    </div>

    <div class="rm-container">
        <!-- Status Card -->
        <div class="rm-card rm-status-card">
            <div class="rm-card-header">
                <h2><i class="dashicons dashicons-admin-settings"></i> Status do Sistema</h2>
            </div>
            <div class="rm-card-body">
                <div class="rm-status-item">
                    <label class="rm-toggle">
                        <input type="checkbox" id="rm-enabled" <?php checked($enabled); ?>>
                        <span class="rm-toggle-slider"></span>
                    </label>
                    <div class="rm-status-info">
                        <strong>Sistema de Redirecionamento</strong>
                        <span class="rm-status-desc">Ativar/desativar todos os redirecionamentos</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="rm-grid">
            <!-- Configurações Principais -->
            <div class="rm-card">
                <div class="rm-card-header">
                    <h2><i class="dashicons dashicons-admin-page"></i> Páginas Principais</h2>
                </div>
                <div class="rm-card-body">
                    <div class="rm-field-group">
                        <label for="rm-login-page">Página de Login</label>
                        <input type="text" id="rm-login-page" value="<?php echo esc_attr($login_page); ?>" placeholder="acessar">
                        <small>Slug da página de login</small>
                    </div>


                </div>
            </div>

            <!-- Exceções -->
            <div class="rm-card">
                <div class="rm-card-header">
                    <h2><i class="dashicons dashicons-unlock"></i> Páginas Públicas</h2>
                </div>
                <div class="rm-card-body">
                    <p class="rm-card-desc">Páginas que usuários não logados podem acessar sem redirecionamento</p>
                    
                    <div class="rm-add-exception">
                        <input type="text" id="rm-new-exception" placeholder="Digite o slug da página">
                        <button type="button" id="rm-add-exception-btn" class="rm-btn rm-btn-primary">
                            <i class="dashicons dashicons-plus-alt"></i> Adicionar
                        </button>
                    </div>

                    <div class="rm-exceptions-list">
                        <?php foreach ($exceptions as $exception): ?>
                            <div class="rm-exception-item" data-page="<?php echo esc_attr($exception); ?>">
                                <span class="rm-exception-name"><?php echo esc_html($exception); ?></span>
                                <button type="button" class="rm-remove-exception" data-page="<?php echo esc_attr($exception); ?>">
                                    <i class="dashicons dashicons-no-alt"></i>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>



        <!-- Botões de Ação -->
        <div class="rm-actions">
            <button type="button" id="rm-save-settings" class="rm-btn rm-btn-success">
                <i class="dashicons dashicons-yes"></i> Salvar Configurações
            </button>
            <button type="button" id="rm-reset-settings" class="rm-btn rm-btn-secondary">
                <i class="dashicons dashicons-backup"></i> Restaurar Padrões
            </button>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="rm-loading" class="rm-loading" style="display: none;">
        <div class="rm-loading-content">
            <div class="rm-spinner"></div>
            <p>Salvando configurações...</p>
        </div>
    </div>


</div>