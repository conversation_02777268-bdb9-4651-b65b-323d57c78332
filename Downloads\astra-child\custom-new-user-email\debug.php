<?php
/**
 * Arquivo de Debug para Custom New User Email Plugin
 * Use este arquivo para testar e diagnosticar problemas
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class CNUEDebug {
    
    public static function check_plugin_status() {
        echo "<h2>Status do Plugin Custom New User Email</h2>";
        
        // Verificar se o plugin está ativo
        echo "<h3>1. Status de Ativação</h3>";
        echo "Plugin ativo: " . (class_exists('CustomNewUserEmail') ? "✅ SIM" : "❌ NÃO") . "<br>";
        
        // Verificar configurações
        echo "<h3>2. Configurações</h3>";
        $email_enabled = get_option('cnue_email_enabled', false);
        echo "Email personalizado habilitado: " . ($email_enabled ? "✅ SIM" : "❌ NÃO") . "<br>";
        echo "Assunto do email: " . get_option('cnue_email_subject', 'Não definido') . "<br>";
        echo "Email do remetente: " . get_option('cnue_email_from_email', 'Não definido') . "<br>";
        echo "Nome do remetente: " . get_option('cnue_email_from_name', 'Não definido') . "<br>";
        
        // Verificar template
        echo "<h3>3. Template</h3>";
        $template_path = plugin_dir_path(__FILE__) . 'templates/email-template.php';
        echo "Template existe: " . (file_exists($template_path) ? "✅ SIM" : "❌ NÃO") . "<br>";
        echo "Caminho do template: " . $template_path . "<br>";
        
        // Verificar hooks
        echo "<h3>4. Hooks Registrados</h3>";
        global $wp_filter;
        
        $hooks_to_check = [
            'wp_new_user_notification_email',
            'wp_mail_content_type',
            'wp_mail_from',
            'wp_mail_from_name',
            'user_register'
        ];
        
        foreach ($hooks_to_check as $hook) {
            if (isset($wp_filter[$hook])) {
                echo "Hook '{$hook}': ✅ REGISTRADO<br>";
                foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                    foreach ($callbacks as $callback) {
                        if (is_array($callback['function']) && 
                            is_object($callback['function'][0]) && 
                            get_class($callback['function'][0]) === 'CustomNewUserEmail') {
                            echo "&nbsp;&nbsp;- Método: {$callback['function'][1]} (Prioridade: {$priority})<br>";
                        }
                    }
                }
            } else {
                echo "Hook '{$hook}': ❌ NÃO REGISTRADO<br>";
            }
        }
        
        // Verificar configuração de email do WordPress
        echo "<h3>5. Configuração de Email do WordPress</h3>";
        echo "Email admin: " . get_option('admin_email') . "<br>";
        echo "Nome do site: " . get_bloginfo('name') . "<br>";
        echo "URL do site: " . get_site_url() . "<br>";
        
        // Teste de envio de email simples
        echo "<h3>6. Teste de Email Básico</h3>";
        $test_email = get_option('admin_email');
        $test_sent = wp_mail($test_email, 'Teste CNUE - Email Básico', 'Este é um teste básico de email do WordPress.');
        echo "Teste de email básico: " . ($test_sent ? "✅ SUCESSO" : "❌ FALHOU") . "<br>";
        
        // Verificar logs recentes
        echo "<h3>7. Logs Recentes (últimas 50 linhas)</h3>";
        $log_file = WP_CONTENT_DIR . '/debug.log';
        if (file_exists($log_file)) {
            $logs = file($log_file);
            $recent_logs = array_slice($logs, -50);
            $cnue_logs = array_filter($recent_logs, function($line) {
                return strpos($line, 'CNUE:') !== false;
            });
            
            if (!empty($cnue_logs)) {
                echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: auto;'>";
                foreach ($cnue_logs as $log) {
                    echo htmlspecialchars($log);
                }
                echo "</pre>";
            } else {
                echo "Nenhum log CNUE encontrado nos logs recentes.<br>";
            }
        } else {
            echo "Arquivo de log não encontrado. Verifique se WP_DEBUG_LOG está habilitado.<br>";
        }
    }
    
    public static function test_email_creation() {
        echo "<h2>Teste de Criação de Email</h2>";
        
        // Criar dados fictícios
        $user = (object) [
            'ID' => 999,
            'user_login' => 'teste_usuario',
            'user_email' => get_option('admin_email'),
            'user_pass' => 'senha123',
            'display_name' => 'Usuário Teste',
            'first_name' => 'Usuário',
            'last_name' => 'Teste'
        ];
        
        echo "Testando geração de email com dados fictícios...<br>";
        echo "Usuário: " . $user->user_login . "<br>";
        echo "Email: " . $user->user_email . "<br>";
        
        // Tentar gerar o template
        $template_path = plugin_dir_path(__FILE__) . 'templates/email-template.php';
        if (file_exists($template_path)) {
            ob_start();
            $login_url = wp_login_url();
            include $template_path;
            $html_content = ob_get_clean();
            
            echo "<h3>Template Gerado com Sucesso</h3>";
            echo "<details><summary>Ver HTML Gerado</summary>";
            echo "<textarea style='width: 100%; height: 300px;'>" . htmlspecialchars($html_content) . "</textarea>";
            echo "</details>";
            
            // Tentar enviar email de teste
            $subject = get_option('cnue_email_subject', 'Teste CNUE');
            $from_name = get_option('cnue_email_from_name', get_bloginfo('name'));
            $from_email = get_option('cnue_email_from_email', get_option('admin_email'));
            
            $headers = [
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $from_name . ' <' . $from_email . '>'
            ];
            
            $sent = wp_mail($user->user_email, $subject . ' (TESTE DEBUG)', $html_content, $headers);
            echo "<h3>Envio de Email de Teste</h3>";
            echo "Resultado: " . ($sent ? "✅ SUCESSO" : "❌ FALHOU") . "<br>";
            
        } else {
            echo "❌ Template não encontrado: " . $template_path . "<br>";
        }
    }
    
    public static function show_debug_info() {
        if (!current_user_can('manage_options')) {
            wp_die('Você não tem permissão para acessar esta página.');
        }
        
        echo "<div style='margin: 20px; font-family: Arial, sans-serif;'>";
        echo "<h1>Debug - Custom New User Email Plugin</h1>";
        
        self::check_plugin_status();
        echo "<hr>";
        self::test_email_creation();
        
        echo "</div>";
    }
}

// Se acessado diretamente via URL (para debug)
if (isset($_GET['cnue_debug']) && current_user_can('manage_options')) {
    add_action('wp_loaded', function() {
        CNUEDebug::show_debug_info();
        exit;
    });
}