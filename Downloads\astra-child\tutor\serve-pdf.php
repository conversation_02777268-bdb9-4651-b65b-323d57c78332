<?php
require_once($_SERVER['DOCUMENT_ROOT'].'/wp-load.php');

if (!isset($_GET['id']) || !is_user_logged_in()) {
    status_header(403);
    die('Acesso negado.');
}

$pdf_id = intval($_GET['id']);
$pdf_path = get_attached_file($pdf_id);

if (!$pdf_path || !file_exists($pdf_path)) {
    status_header(404);
    die('Arquivo não encontrado.');
}

if (ob_get_level()) {
    ob_end_clean();
}

header('Content-Type: application/pdf');
header('Content-Length: ' . filesize($pdf_path));
header('Content-Disposition: inline; filename="' . basename($pdf_path) . '"');

readfile($pdf_path);
exit;