/* Estilos para o Widget do Botão de Certificado */

.elementor-widget-button_certificate_courses .tutor-btn-wrapper {
    display: block;
    width: 100%;
}

.elementor-widget-button_certificate_courses .tutor-btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    transition: all 0.15s ease-in-out;
}

.elementor-widget-button_certificate_courses .tutor-btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.elementor-widget-button_certificate_courses .tutor-btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #0056b3;
}

.elementor-widget-button_certificate_courses .tutor-mb-20 {
    margin-bottom: 20px;
}

/* Estilo para o botão desativado (curso não completado) */
.elementor-widget-button_certificate_courses .tutor-btn-view-certificate[style*="opacity: 0.5"] {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

/* Responsividade */
@media (max-width: 768px) {
    .elementor-widget-button_certificate_courses .tutor-btn {
        font-size: 0.9rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Classes utilitárias para personalização adicional */
.elementor-widget-button_certificate_courses .tutor-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.elementor-widget-button_certificate_courses .tutor-btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
}

.elementor-widget-button_certificate_courses .tutor-btn-rounded {
    border-radius: 50px;
}

.elementor-widget-button_certificate_courses .tutor-btn-shadow {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}