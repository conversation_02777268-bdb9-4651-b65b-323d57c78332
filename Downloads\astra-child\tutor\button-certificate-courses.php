<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

class Button_Certificate_Courses_Widget extends Widget_Base {

    public function get_name() {
        return 'button_certificate_courses';
    }

    public function get_title() {
        return esc_html__( 'Botão Certificado', 'membero' );
    }

    public function get_icon() {
        return 'eicon-button';
    }

    public function get_categories() {
        return [ 'custom_category' ];
    }

protected function register_controls() {
    $this->start_controls_section(
        'content_section',
        [
            'label' => esc_html__( 'Conteúdo', 'membero' ),
            'tab' => Controls_Manager::TAB_CONTENT,
        ]
    );


    $this->add_control(
        'course_id',
        [
            'label' => esc_html__( 'ID do Curso', 'membero' ),
            'type' => Controls_Manager::NUMBER,
            'default' => '',
            'separator' => 'before',
            'description' => esc_html__( 'Insira o ID do curso para o qual este botão de certificado se aplica.', 'membero' ),
        ]
    );

    $this->add_control(
        'button_text',
        [
            'label' => esc_html__( 'Texto do Botão', 'membero' ),
            'type' => Controls_Manager::TEXT,
            'default' => esc_html__( 'Ver Certificado', 'membero' ),
            'label_block' => true,
            'separator' => 'before',
        ]
    );

    $this->add_control(
        'incomplete_text',
        [
            'label' => esc_html__( 'Texto para Curso Incompleto', 'membero' ),
            'type' => Controls_Manager::TEXT,
            'default' => esc_html__( 'Você ainda não completou nenhum curso.', 'membero' ),
            'label_block' => true,
            'separator' => 'before',
        ]
    );

    $this->end_controls_section();

        $this->start_controls_section(
            'style_section',
            [
                'label' => esc_html__( 'Estilo', 'membero' ),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );

                $this->add_control(
            'button_position',
            [
                'label' => esc_html__( 'Alinhamento', 'membero' ),
                'type' => Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => esc_html__( 'Esquerda', 'membero' ),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => esc_html__( 'Centro', 'membero' ),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => esc_html__( 'Direita', 'membero' ),
                        'icon' => 'eicon-text-align-right',
                    ],
                    'justify' => [
                        'title' => esc_html__( 'Esticar', 'membero' ),
                        'icon' => 'eicon-text-align-justify',
                    ],
                ],
                'default' => 'left',
                'toggle' => true,
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn-wrapper' => 'text-align: {{VALUE}};',
                    '{{WRAPPER}} .tutor-btn' => '{{VALUE}} === "justify" ? "width: 100%" : ""',
                ],
            ]
        );
        
                $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'label' => esc_html__( 'Borda', 'membero' ),
                'selector' => '{{WRAPPER}} .tutor-btn',
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => esc_html__( 'Raio da Borda', 'membero' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%' ],
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );


        $this->add_control(
            'button_background_color',
            [
                'label' => esc_html__( 'Cor de Fundo', 'membero' ),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => esc_html__( 'Cor do Texto', 'membero' ),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'selector' => '{{WRAPPER}} .tutor-btn',
            ]
        );

        $this->add_control(
            'button_width',
            [
                'label' => esc_html__( 'Largura do Botão', 'membero' ),
                'type' => Controls_Manager::SLIDER,
                'size_units' => [ 'px', '%' ],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 1000,
                        'step' => 1,
                    ],
                    '%' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn' => 'width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );
        
        

        $this->add_control(
            'button_padding',
            [
                'label' => esc_html__( 'Preenchimento', 'membero' ),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%' ],
                'selectors' => [
                    '{{WRAPPER}} .tutor-btn' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
        


    }

    protected function render() {
        $settings = $this->get_settings_for_display();

        if (!function_exists('tutor_utils')) {
            echo 'TutorLMS não está ativo.';
            return;
        }

        $course_id = intval($settings['course_id']);

        // Verificar se o certificado está desabilitado
        $disable_certificate = get_post_meta($course_id, '_tutor_disable_certificate', true);
        $certificate_template = get_post_meta($course_id, 'tutor_course_certificate_template', true);
        if ($certificate_template == 'none' || (!$certificate_template && $disable_certificate == 'yes')) {
            return; // Não mostrar o botão se o certificado estiver desabilitado
        }

        // Verificar se o usuário completou o curso
        $completed = tutor_utils()->is_completed_course($course_id);

        if (!$completed) {
            echo '<div class="tutor-btn tutor-btn-primary tutor-btn-block tutor-mb-20 tutor-btn-view-certificate" style="opacity: 0.5; cursor: not-allowed;">' . esc_html($settings['incomplete_text']) . '</div>';
            return;
        }

        // Gerar URL do certificado
        $cert_hash = $completed->completed_hash;
        $certificate_url = add_query_arg(
            array('cert_hash' => $cert_hash),
            site_url('tutor-certificados')
        );

        // Gerar o HTML do botão
       $button_html = sprintf(
            '<div class="tutor-btn-wrapper"><a href="%s" class="tutor-btn tutor-btn-primary tutor-mb-20 tutor-btn-view-certificate">%s</a></div>',
            esc_url(add_query_arg(array('regenerate' => 1), $certificate_url)),
            esc_html($settings['button_text'])
        );

        echo $button_html;
    }
}

// Registrar o widget Button Certificate Courses
function register_button_certificate_courses_widget( $widgets_manager ) {
    $widgets_manager->register( new Button_Certificate_Courses_Widget() );
}
add_action( 'elementor/widgets/register', 'register_button_certificate_courses_widget' );

// Adicionar categoria personalizada Button Certificate Courses
function add_button_certificate_courses_category( $elements_manager ) {
    $elements_manager->add_category(
        'custom_category',
        [
            'title' => esc_html__( 'Membero', 'membero' ),
            'icon' => 'fa fa-plug',
        ]
    );
}
add_action( 'elementor/elements/categories_registered', 'add_button_certificate_courses_category' );