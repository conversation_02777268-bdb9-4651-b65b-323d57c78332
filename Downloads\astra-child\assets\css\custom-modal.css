.custom-modal {
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.custom-modal.show {
    opacity: 1;
    visibility: visible;
}

.custom-modal-content {
    background-color: #fefefe;
    color: #000 !important;
    padding: 20px;
    border: 1px solid #888;
    max-width: 500px;
    width: 90%;
    text-align: center;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1060;
    margin: 0 auto;
}


.custom-modal-content h3{
    color: #000 !important;
    margin-bottom: 0px !important;
}

.custom-modal-content p {
    color: #000 !important;
    margin-bottom: 0px !important;
}

.custom-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.custom-close:hover,
.custom-close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal-header {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.hourglass-icon {
    width: 50px;
    height: 50px;
}

.modal-body p {
    margin: 10px 0;
}

.modal-body #modalReleaseDate {
    font-weight: bold;
    margin-top: 0px !important;
}

/* Ajustes para dispositivos menores */
@media (max-width: 767px) {
    .custom-modal-content {
        width: 85%;
    }
}

@media (max-width: 430px) {
    .custom-modal-content {
        width: 80%;
    }
}
