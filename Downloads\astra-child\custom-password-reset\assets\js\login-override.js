/**
 * WordPress Login Override JavaScript
 */
(function($) {
    'use strict';

    class LoginOverride {
        constructor() {
            this.currentTheme = localStorage.getItem('cpr-theme') || 'dark';
            this.init();
        }

        init() {
            this.setupTheme();
            this.setupThemeToggle();
            this.setupFormEnhancements();
            this.setupAccessibility();
            this.setupAnimations();
        }

        setupTheme() {
            // Apply theme to body
            document.body.classList.remove('cpr-theme-dark', 'cpr-theme-light');
            document.body.classList.add(`cpr-theme-${this.currentTheme}`);
        }

        setupThemeToggle() {
            // The theme toggle is created via CSS pseudo-element
            // We need to create an invisible button to handle clicks
            if (!document.querySelector('.cpr-theme-toggle-btn')) {
                const toggle = document.createElement('button');
                toggle.className = 'cpr-theme-toggle-btn';
                toggle.style.position = 'fixed';
                toggle.style.top = '20px';
                toggle.style.right = '20px';
                toggle.style.width = '50px';
                toggle.style.height = '50px';
                toggle.style.background = 'transparent';
                toggle.style.border = 'none';
                toggle.style.cursor = 'pointer';
                toggle.style.zIndex = '1001';
                toggle.setAttribute('aria-label', 'Alternar tema');
                document.body.appendChild(toggle);
            }

            // Handle theme toggle
            $(document).on('click', '.cpr-theme-toggle-btn', (e) => {
                e.preventDefault();
                this.toggleTheme();
            });
        }

        toggleTheme() {
            this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('cpr-theme', this.currentTheme);
            
            this.setupTheme();
            
            // Animate theme change
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);

            // Announce to screen readers
            this.announceToScreenReader(`Tema alterado para ${this.currentTheme === 'dark' ? 'escuro' : 'claro'}`);
        }

        setupFormEnhancements() {
            // Add logo and title to form
            this.addLogoAndTitle();

            // Add placeholders to inputs
            this.addPlaceholders();

            // Enhance form validation
            this.setupValidation();

            // Add loading states
            this.setupLoadingStates();

            // Auto-focus first input
            this.autoFocusFirstInput();
        }

        addLogoAndTitle() {
            // Adicionar título após a logo
            const $forms = $('#loginform, #lostpasswordform, #resetpassform');

            $forms.each(function() {
                const $form = $(this);

                // Verificar se já tem título
                if (!$form.find('.cpr-title').length) {
                    let titleText = 'Recuperar Acesso';

                    if ($form.is('#loginform')) {
                        titleText = 'Fazer Login';
                    } else if ($form.is('#resetpassform')) {
                        titleText = 'Redefinir Senha';
                    }

                    // Adicionar título
                    $form.prepend(`<h1 class="cpr-title">${titleText}</h1>`);
                }
            });
        }

        addPlaceholders() {
            // Add placeholders based on input context
            $('#user_login').attr('placeholder', 'E-mail corporativo');
            $('#user_pass').attr('placeholder', 'Sua senha');
            $('#pass1').attr('placeholder', 'Nova senha');
            $('#pass2').attr('placeholder', 'Confirmar nova senha');
            
            // For lost password form
            if ($('#lostpasswordform').length) {
                $('#user_login').attr('placeholder', 'Nome de usuário ou e-mail');
            }
        }

        setupValidation() {
            // Real-time email validation
            $('#user_login').on('input blur', function() {
                const $input = $(this);
                const value = $input.val().trim();
                
                $input.removeClass('error success');
                
                if (value && $input.attr('type') !== 'text') {
                    if (this.isValidEmail(value)) {
                        $input.addClass('success');
                    } else {
                        $input.addClass('error');
                    }
                }
            }.bind(this));

            // Password strength indicator
            $('#pass1').on('input', function() {
                const password = $(this).val();
                this.showPasswordStrength(password);
            }.bind(this));

            // Password confirmation
            $('#pass2').on('input blur', function() {
                const $confirm = $(this);
                const $password = $('#pass1');
                const confirmValue = $confirm.val();
                const passwordValue = $password.val();
                
                $confirm.removeClass('error success');
                
                if (confirmValue) {
                    if (confirmValue === passwordValue) {
                        $confirm.addClass('success');
                    } else {
                        $confirm.addClass('error');
                    }
                }
            });
        }

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        showPasswordStrength(password) {
            // Remove existing strength indicator
            $('.password-strength').remove();
            
            if (!password) return;
            
            let strength = 0;
            let strengthText = '';
            let strengthClass = '';
            
            // Calculate strength
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            // Determine strength level
            if (strength < 2) {
                strengthText = 'Fraca';
                strengthClass = 'weak';
            } else if (strength < 4) {
                strengthText = 'Média';
                strengthClass = 'medium';
            } else {
                strengthText = 'Forte';
                strengthClass = 'strong';
            }
            
            // Add strength indicator
            const indicator = $(`<div class="password-strength ${strengthClass}">Força da senha: ${strengthText}</div>`);
            $('#pass1').after(indicator);
        }

        setupLoadingStates() {
            // Add loading state to forms
            $('form').on('submit', function() {
                const $form = $(this);
                const $button = $form.find('.button-primary');
                
                if ($button.hasClass('loading')) {
                    return false;
                }
                
                $button.addClass('loading')
                       .prop('disabled', true)
                       .text('Processando...');
                
                // Restore button after 10 seconds (fallback)
                setTimeout(() => {
                    this.restoreButton($button);
                }, 10000);
            }.bind(this));
        }

        restoreButton($button) {
            let originalText = 'Enviar';
            
            if ($button.closest('#loginform').length) {
                originalText = 'Entrar';
            } else if ($button.closest('#lostpasswordform').length) {
                originalText = 'Obter nova senha';
            } else if ($button.closest('#resetpassform').length) {
                originalText = 'Redefinir senha';
            }
            
            $button.removeClass('loading')
                   .prop('disabled', false)
                   .text(originalText);
        }

        autoFocusFirstInput() {
            // Auto-focus first visible input
            setTimeout(() => {
                const $firstInput = $('input[type="text"], input[type="email"], input[type="password"]').filter(':visible').first();
                if ($firstInput.length) {
                    $firstInput.focus();
                }
            }, 100);
        }

        setupAccessibility() {
            // Add ARIA labels
            $('#user_login').attr('aria-label', 'Nome de usuário ou e-mail');
            $('#user_pass').attr('aria-label', 'Senha');
            $('#pass1').attr('aria-label', 'Nova senha');
            $('#pass2').attr('aria-label', 'Confirmar nova senha');
            
            // Add role to form
            $('form').attr('role', 'form');
            
            // Keyboard navigation for theme toggle
            $('.cpr-theme-toggle-btn').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });
            
            // Setup ARIA live region
            this.setupAriaLiveRegion();
            
            // Announce errors to screen readers
            this.announceErrors();
        }

        setupAriaLiveRegion() {
            if (!document.getElementById('login-aria-live')) {
                const ariaLive = document.createElement('div');
                ariaLive.id = 'login-aria-live';
                ariaLive.setAttribute('aria-live', 'polite');
                ariaLive.setAttribute('aria-atomic', 'true');
                ariaLive.style.position = 'absolute';
                ariaLive.style.left = '-10000px';
                ariaLive.style.width = '1px';
                ariaLive.style.height = '1px';
                ariaLive.style.overflow = 'hidden';
                document.body.appendChild(ariaLive);
            }
        }

        announceToScreenReader(message) {
            const ariaLive = document.getElementById('login-aria-live');
            if (ariaLive) {
                ariaLive.textContent = message;
            }
        }

        announceErrors() {
            // Announce existing errors
            const $error = $('#login_error');
            if ($error.length) {
                this.announceToScreenReader('Erro: ' + $error.text().trim());
            }
            
            // Announce success messages
            const $message = $('.message');
            if ($message.length) {
                this.announceToScreenReader('Sucesso: ' + $message.text().trim());
            }
        }

        setupAnimations() {
            // Add entrance animation
            $('#login').addClass('animate-in');
            
            // Animate messages
            $('.message, #login_error').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });
            
            // Smooth scroll to errors
            if ($('#login_error').length) {
                $('html, body').animate({
                    scrollTop: $('#login_error').offset().top - 20
                }, 500);
            }
        }

        // Public methods
        setTheme(theme) {
            if (['dark', 'light'].includes(theme)) {
                this.currentTheme = theme;
                localStorage.setItem('cpr-theme', theme);
                this.setupTheme();
            }
        }

        getTheme() {
            return this.currentTheme;
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        window.loginOverride = new LoginOverride();
        
        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            $('.message, #login_error').fadeOut(500);
        }, 5000);
        
        // Add custom CSS for password strength
        const strengthCSS = `
            <style>
                .password-strength {
                    font-size: 12px;
                    margin-top: 5px;
                    padding: 5px;
                    border-radius: 4px;
                    text-align: center;
                }
                .password-strength.weak {
                    background: rgba(255, 107, 107, 0.1);
                    color: #ff6b6b;
                    border: 1px solid #ff6b6b;
                }
                .password-strength.medium {
                    background: rgba(255, 193, 7, 0.1);
                    color: #ffc107;
                    border: 1px solid #ffc107;
                }
                .password-strength.strong {
                    background: rgba(81, 207, 102, 0.1);
                    color: #51cf66;
                    border: 1px solid #51cf66;
                }
                input.error {
                    border-color: #ff6b6b !important;
                }
                input.success {
                    border-color: #51cf66 !important;
                }
                .button-primary.loading {
                    opacity: 0.7;
                    cursor: not-allowed;
                }
                #login.animate-in {
                    animation: loginFadeIn 0.6s ease-out;
                }
                @keyframes loginFadeIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50%) translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(-50%) translateY(0);
                    }
                }
            </style>
        `;
        $('head').append(strengthCSS);
    });

    // Handle page visibility change
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden && window.loginOverride) {
            window.loginOverride.setupTheme();
        }
    });

})(jQuery);
