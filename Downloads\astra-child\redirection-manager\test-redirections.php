<?php
/**
 * Arquivo de teste para verificar redirecionamentos
 * REMOVER EM PRODUÇÃO
 */

if (!defined('ABSPATH')) {
    exit;
}

// Adicionar página de teste no admin (apenas para desenvolvimento)
add_action('admin_menu', function() {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        add_submenu_page(
            'redirection-manager',
            'Teste de Redirecionamentos',
            'Teste',
            'manage_options',
            'rm-test',
            'rm_test_page'
        );
    }
});

function rm_test_page() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }
    
    $settings = [
        'enabled' => get_option('rm_redirection_enabled', true),
        'login_page' => get_option('rm_login_page', 'acessar'),
        'register_page' => get_option('rm_register_page', 'cadastro'),
        'member_area' => get_option('rm_member_area', 'area-de-membros'),
        'settings_page' => get_option('rm_settings_page', 'painel/settings/'),
        'exceptions' => get_option('rm_exceptions', ['cursos']),
        'admin_redirections' => get_option('rm_admin_redirections', [])
    ];
    
    ?>
    <div class="wrap">
        <h1>Teste de Redirecionamentos</h1>
        
        <div class="card">
            <h2>Configurações Atuais</h2>
            <table class="form-table">
                <tr>
                    <th>Sistema Ativo:</th>
                    <td><?php echo $settings['enabled'] ? '✅ Sim' : '❌ Não'; ?></td>
                </tr>
                <tr>
                    <th>Página de Login:</th>
                    <td><code><?php echo esc_html($settings['login_page']); ?></code></td>
                </tr>
                <tr>
                    <th>Página de Cadastro:</th>
                    <td><code><?php echo esc_html($settings['register_page']); ?></code></td>
                </tr>
                <tr>
                    <th>Área de Membros:</th>
                    <td><code><?php echo esc_html($settings['member_area']); ?></code></td>
                </tr>
                <tr>
                    <th>Página de Configurações:</th>
                    <td><code><?php echo esc_html($settings['settings_page']); ?></code></td>
                </tr>
                <tr>
                    <th>Exceções:</th>
                    <td>
                        <?php foreach ($settings['exceptions'] as $exception): ?>
                            <code><?php echo esc_html($exception); ?></code> 
                        <?php endforeach; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Links de Teste</h2>
            <p><strong>Teste estes links em uma aba anônima/privada:</strong></p>
            <ul>
                <li><a href="<?php echo site_url('/' . $settings['login_page']); ?>" target="_blank">Página de Login</a></li>
                <li><a href="<?php echo site_url('/' . $settings['register_page']); ?>" target="_blank">Página de Cadastro</a></li>
                <li><a href="<?php echo site_url('/painel'); ?>" target="_blank">Painel (deve redirecionar)</a></li>
                <li><a href="<?php echo site_url('/courses'); ?>" target="_blank">Courses (deve redirecionar)</a></li>
                <?php foreach ($settings['exceptions'] as $exception): ?>
                    <li><a href="<?php echo site_url('/' . $exception); ?>" target="_blank"><?php echo ucfirst($exception); ?> (exceção)</a></li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <div class="card">
            <h2>Status da Migração</h2>
            <table class="form-table">
                <tr>
                    <th>Migração Concluída:</th>
                    <td><?php echo get_option('rm_migration_completed') ? '✅ Sim' : '❌ Não'; ?></td>
                </tr>
                <tr>
                    <th>Aviso Dispensado:</th>
                    <td><?php echo get_option('rm_migration_notice_dismissed') ? '✅ Sim' : '❌ Não'; ?></td>
                </tr>
            </table>
        </div>
    </div>
    
    <style>
        .card {
            background: white;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        .card h2 {
            margin-top: 0;
        }
        code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
    <?php
}