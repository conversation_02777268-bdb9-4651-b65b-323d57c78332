# Redirection Manager

Plugin para gerenciar todos os redirecionamentos do site de forma centralizada com interface administrativa moderna e intuitiva.

## Funcionalidades

### ✅ Gerenciamento Centralizado
- Painel administrativo único para todos os redirecionamentos
- Interface clean, minimalista e focada em UX
- Configurações organizadas por categorias

### ✅ Redirecionamentos Principais
- **Página de Login**: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (padrão: `acessar`)
- **Página de Cadastro**: Confi<PERSON><PERSON><PERSON><PERSON> (padrão: `cadastro`)
- **Área de Membros**: Configur<PERSON>vel (padrão: `area-de-membros`)
- **Página de Configurações**: Configur<PERSON>vel (padrão: `painel/settings/`)

### ✅ Exceções Personalizáveis
- Lista de páginas públicas que usuários não logados podem acessar
- Adição/remoção dinâmica de exceções
- Exceção padrão: página `cursos`

### ✅ Redirecionamentos Automáticos
- Usuários não logados → Página de login
- Usuários logados em páginas de login/cadastro → Área de membros
- Página `/painel` → Página de configurações
- Página `/courses` → Área de membros
- Integração com TutorLMS

### ✅ Recursos Avançados
- Detecção automática de requisições do Elementor
- Sistema de migração automática das configurações antigas
- Interface responsiva e moderna
- Validação em tempo real
- Notificações de sucesso/erro
- Atalhos de teclado (Ctrl+S para salvar)

## Instalação

1. O plugin é carregado automaticamente pelo tema
2. Acesse **Redirecionamentos** no menu administrativo do WordPress
3. Configure as páginas e exceções conforme necessário

## Migração Automática

O plugin detecta automaticamente configurações existentes no `functions.php` e migra para o novo sistema:

- `redirection_enabled` → `rm_redirection_enabled`
- `redirection_login_page` → `rm_login_page`
- `redirection_register_page` → `rm_register_page`
- `redirection_member_area` → `rm_member_area`

## Estrutura do Plugin

```
redirection-manager/
├── redirection-manager.php    # Arquivo principal
├── migration.php             # Script de migração
├── templates/
│   └── admin-page.php        # Template da página administrativa
├── assets/
│   ├── admin.css            # Estilos do painel
│   └── admin.js             # JavaScript do painel
└── README.md                # Documentação
```

## Uso da Interface

### Status do Sistema
Toggle para ativar/desativar todos os redirecionamentos de uma vez.

### Páginas Principais
Configure os slugs das páginas principais:
- **Login**: Página onde usuários fazem login
- **Cadastro**: Página de registro de novos usuários  
- **Área de Membros**: Área restrita para membros
- **Configurações**: Página de configurações do painel

### Páginas Públicas
Adicione páginas que usuários não logados podem acessar sem redirecionamento:
- Digite o slug da página
- Clique em "Adicionar" ou pressione Enter
- Remova exceções clicando no ícone X

### Redirecionamentos Personalizados
Visualize os redirecionamentos automáticos configurados:
- `/painel` → `/painel/settings/`
- `/courses` → `/area-de-membros`

## Compatibilidade

- ✅ WordPress 5.0+
- ✅ TutorLMS
- ✅ Elementor (detecção automática)
- ✅ Todos os principais plugins de cache
- ✅ Responsive design

## Segurança

- Validação de nonce em todas as requisições AJAX
- Sanitização de todos os inputs
- Verificação de permissões de usuário
- Prevenção de acesso direto aos arquivos

## Suporte

Para suporte técnico ou dúvidas sobre o plugin, entre em contato com a equipe de desenvolvimento.