jQuery(document).ready(function($) {
    'use strict';

    // Verificar se há configurações antigas e mostrar aviso de migração
    if (typeof rm_migration_notice !== 'undefined' && rm_migration_notice.show) {
        showMigrationNotice();
    }

    function showMigrationNotice() {
        const notice = $(`
            <div class="notice notice-info is-dismissible rm-migration-notice">
                <p>
                    <strong>Redirection Manager:</strong> 
                    Detectamos configurações antigas de redirecionamento. 
                    <a href="${rm_migration_notice.admin_url}" class="button button-primary">
                        Acessar Configurações
                    </a>
                </p>
            </div>
        `);
        
        $('.wrap h1').first().after(notice);
        
        // Remover aviso ao clicar no X
        notice.on('click', '.notice-dismiss', function() {
            $.post(ajaxurl, {
                action: 'dismiss_rm_migration_notice',
                nonce: rm_migration_notice.nonce
            });
        });
    }
});