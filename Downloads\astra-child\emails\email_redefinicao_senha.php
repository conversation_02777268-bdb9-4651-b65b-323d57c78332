<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

return function( $content, $user_data, $reset_url ) {
    // Extrair o primeiro nome do usuário
    $first_name = get_user_meta( $user_data->ID, 'first_name', true );

    // Se não houver primeiro nome, usar o nome de usuário como fallback
    if ( empty( $first_name ) ) {
        $first_name = $user_data->user_login;
    }

    // Obter a URL personalizada da logo ou a logo padrão
    $logo_url = get_option('custom_logo_url');
    if (!$logo_url) {
        $logo_id = get_theme_mod('custom_logo');
        $logo_url = $logo_id ? wp_get_attachment_image_src($logo_id, 'full')[0] : '';
    }

    // Substituir as variáveis pelo conteúdo personalizado
    $placeholders = [
        '{{wp-first-name}}' => esc_html( $first_name ),
        '{{wp-email}}' => esc_html( $user_data->user_email ),
        '{admin_email}' => esc_html( get_option( 'admin_email' ) ),
        '{site_name}' => esc_html( get_bloginfo( 'name' ) ),
        '{reset_link}' => esc_url( $reset_url ),
        '{logo}' => esc_url( $logo_url ),
    ];

    // Título personalizado do e-mail
    $subject = 'Redefinição de Senha - {site_name}';
    $subject = str_replace( array_keys( $placeholders ), array_values( $placeholders ), $subject );

    // Corpo do e-mail
    $template = '
    <div style="font-family: Arial, sans-serif; line-height: 1.6; max-width: 560px;margin: 40px auto; text-align: center;">
        <div style=" margin-bottom: 20px;">
            <img src="{logo}" alt="{site_name}" style="width: 150px; height: auto;" />
        
        <p>Olá {{wp-first-name}},</p>
        <p>Você solicitou a redefinição de sua senha para acessar o <strong>{site_name}</strong>. Seu e-mail registrado é: <strong>{{wp-email}}</strong>. Para redefinir sua senha, clique no link abaixo:</p>
        <p style="text-align: center; margin: 20px 0;">
            <a href="{reset_link}" style="background-color: #000; color: #ffffff; padding: 12px 30px; text-decoration: none; border-radius: 5px;">Redefinir Senha</a>
        </p>
        <div style="margin-top: 25px; padding: 15px; background: #eee; border-radius: 3px; text-align: center; font-size: 15px;">Caso não tenha solicitado, ignore essa mensagem.</div>
        
        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            <p>Atenciosamente,<br>{site_name}</p>
        </div>
    </div></div>';

     // Substituir placeholders no conteúdo do e-mail
    $content = str_replace( array_keys( $placeholders ), array_values( $placeholders ), $template );

    // Retornar o título e o conteúdo do e-mail
    return [
        'subject' => $subject,
        'content' => $content,
    ];
};