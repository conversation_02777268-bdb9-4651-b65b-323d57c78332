/**
 * Estilos adicionais para emails - Design Clean e Minimal
 * Estilos que podem ser injetados inline para melhor compatibilidade
 */

/* Reset para clientes de email */
.email-reset {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* Container principal */
.email-wrapper {
    width: 100% !important;
    background-color: #f8f9fa !important;
    padding: 20px 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
}

/* Container do email */
.email-container {
    max-width: 600px !important;
    margin: 0 auto !important;
    background-color: #ffffff !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Cabeçalho */
.email-header {
    padding: 40px 30px !important;
    text-align: center !important;
    color: white !important;
}

.email-logo {
    max-width: 150px !important;
    height: auto !important;
    margin-bottom: 20px !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.email-header h1 {
    font-size: 28px !important;
    font-weight: 600 !important;
    margin: 0 0 10px 0 !important;
    color: white !important;
}

.email-header p {
    font-size: 16px !important;
    margin: 0 !important;
    opacity: 0.9 !important;
    color: white !important;
}

/* Corpo do email */
.email-body {
    padding: 40px 30px !important;
}

.greeting {
    font-size: 18px !important;
    margin-bottom: 20px !important;
    color: #2c3e50 !important;
    font-weight: 500 !important;
}

.message {
    font-size: 16px !important;
    line-height: 1.8 !important;
    margin-bottom: 30px !important;
    color: #555555 !important;
}

.message p {
    margin: 0 0 15px 0 !important;
}

/* Botão de reset */
.button-container {
    text-align: center !important;
    margin: 30px 0 !important;
}

.reset-button {
    display: inline-block !important;
    color: white !important;
    text-decoration: none !important;
    padding: 16px 32px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    text-align: center !important;
    margin: 20px 0 !important;
    border: none !important;
}

/* Link alternativo */
.alternative-link {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 30px 0 !important;
}

.alternative-link h3 {
    font-size: 16px !important;
    margin: 0 0 10px 0 !important;
    color: #495057 !important;
    font-weight: 600 !important;
}

.alternative-link p {
    font-size: 14px !important;
    color: #6c757d !important;
    word-break: break-all !important;
    margin: 0 !important;
}

/* Aviso de segurança */
.security-notice {
    background-color: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 30px 0 !important;
}

.security-notice h3 {
    color: #856404 !important;
    font-size: 16px !important;
    margin: 0 0 10px 0 !important;
    font-weight: 600 !important;
}

.security-notice p {
    color: #856404 !important;
    font-size: 14px !important;
    margin: 0 0 10px 0 !important;
}

.security-notice p:last-child {
    margin-bottom: 0 !important;
}

/* Rodapé */
.email-footer {
    background-color: #f8f9fa !important;
    padding: 30px !important;
    text-align: center !important;
    border-top: 1px solid #e9ecef !important;
}

.footer-content {
    font-size: 14px !important;
    color: #6c757d !important;
    line-height: 1.6 !important;
}

.footer-content p {
    margin: 0 0 10px 0 !important;
}

.footer-content p:last-child {
    margin-bottom: 0 !important;
}

.footer-content a {
    text-decoration: none !important;
}

/* Responsivo para dispositivos móveis */
@media only screen and (max-width: 600px) {
    .email-wrapper {
        padding: 10px 0 !important;
    }
    
    .email-container {
        width: 100% !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .email-header,
    .email-body,
    .email-footer {
        padding: 20px !important;
    }

    .email-header h1 {
        font-size: 24px !important;
    }

    .reset-button {
        display: block !important;
        width: 100% !important;
        padding: 16px !important;
        box-sizing: border-box !important;
    }
    
    .greeting {
        font-size: 16px !important;
    }
    
    .message {
        font-size: 15px !important;
    }
}

/* Compatibilidade com clientes de email específicos */

/* Outlook */
.outlook-fix {
    mso-table-lspace: 0pt !important;
    mso-table-rspace: 0pt !important;
}

/* Gmail */
.gmail-fix {
    min-width: 100% !important;
}

/* Apple Mail */
.apple-fix {
    -webkit-text-size-adjust: 100% !important;
}