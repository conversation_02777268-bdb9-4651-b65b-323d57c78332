jQuery(document).ready(function($) {
    // Adiciona estilos para as notificações
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .memberolms-notification {
                position: fixed;
                top: 32px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 4px;
                color: white;
                font-weight: 500;
                z-index: 999999;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                animation: slideIn 0.3s ease-out;
            }
            .memberolms-notification-success {
                background-color: #22c55e;
            }
            .memberolms-notification-error {
                background-color: #ef4444;
            }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `)
        .appendTo('head');

    // Inicializa o color picker
    $('.color-field').wpColorPicker();

    // Toggle DRM settings
    $('input[name="drm_enabled"]').on('change', function() {
        const $form = $(this).closest('form');
        const $drmSettings = $form.find('.drm-settings');
        
        if ($(this).is(':checked')) {
            $drmSettings.slideDown(300);
        } else {
            $drmSettings.slideUp(300);
        }
    });

    // Handler para o formulário
    $('.course-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const courseId = $form.data('course-id');
        const $submitButton = $form.find('button[type="submit"]');
        
        
        // Desabilita o botão durante o envio
        $submitButton.prop('disabled', true).text('Salvando...');
        
        // Prepara os dados para envio
        const formData = new FormData();
        formData.append('action', 'save_memberolms_settings');
        formData.append('nonce', memberoLMSAdmin.nonce);
        formData.append('course_id', courseId);
        formData.append('memberolink_redirect_url', $form.find('input[name="memberolink_redirect_url"]').val());
        formData.append('memberolink_custom_url', $form.find('input[name="memberolink_custom_url"]').val());
        formData.append('drm_enabled', $form.find('input[name="drm_enabled"]').is(':checked') ? '1' : '0');
        formData.append('font_size', $form.find('select[name="font_size"]').val());
        formData.append('background_color', $form.find('input[name="background_color"]').val());
        formData.append('background_opacity', $form.find('input[name="background_opacity"]').val());
        formData.append('display_option', $form.find('select[name="display_option"]').val());

// Envia via AJAX
        $.ajax({
            url: memberoLMSAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification('Configurações salvas com sucesso!', 'success');
                } else {
                    showNotification(response.data || 'Erro ao salvar as configurações.', 'error');
                }
            },
            error: function(xhr) {
                console.error('Ajax error:', xhr);
                showNotification('Erro de conexão. Tente novamente.', 'error');
            },
            complete: function() {
                $submitButton.prop('disabled', false).text('Salvar Configurações');
            }
        });
    });


    // Função para mostrar notificações
    function showNotification(message, type = 'success') {
        $('.memberolms-notification').remove();
        
        const $notification = $('<div>', {
            class: `memberolms-notification memberolms-notification-${type}`,
            text: message
        }).appendTo('body');

        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
});
