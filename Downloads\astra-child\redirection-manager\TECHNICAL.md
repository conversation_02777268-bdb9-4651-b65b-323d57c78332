# Documentação Técnica - Redirection Manager

## Arquitetura do Plugin

### Estrutura de Classes

#### `RedirectionManager` (Classe Principal)
- **Responsabilidade**: Gerenciar todo o sistema de redirecionamentos
- **Hooks Principais**:
  - `template_redirect`: Executa lógica de redirecionamento
  - `admin_menu`: Adiciona página administrativa
  - `admin_init`: Registra configurações
  - `admin_enqueue_scripts`: Carrega assets do admin

#### `RedirectionMigration` (Classe de Migração)
- **Responsabilidade**: Migrar configurações antigas para o novo sistema
- **Métodos Principais**:
  - `migrate_settings()`: Transfere configurações
  - `cleanup_old_settings()`: Remove configurações antigas
  - `is_migration_needed()`: Verifica necessidade de migração

### Fluxo de Redirecionamento

```php
template_redirect (hook)
    ↓
handle_redirections()
    ↓
Verificações de segurança:
    - is_admin()
    - wp_doing_ajax()
    - is_elementor_request()
    ↓
Verificar se sistema está ativo
    ↓
Obter configurações do banco
    ↓
Aplicar regras de redirecionamento:
    1. Redirecionamentos administrativos
    2. Redirecionamentos do TutorLMS
    3. Redirecionamentos de usuários logados
    4. Redirecionamentos de usuários não logados
```

### Configurações do Banco de Dados

| Opção | Tipo | Padrão | Descrição |
|-------|------|--------|-----------|
| `rm_redirection_enabled` | boolean | true | Sistema ativo/inativo |
| `rm_login_page` | string | 'acessar' | Slug da página de login |
| `rm_register_page` | string | 'cadastro' | Slug da página de cadastro |
| `rm_member_area` | string | 'area-de-membros' | Slug da área de membros |
| `rm_settings_page` | string | 'painel/settings/' | Caminho das configurações |
| `rm_exceptions` | array | ['cursos'] | Páginas públicas |
| `rm_admin_redirections` | array | {...} | Redirecionamentos personalizados |
| `rm_migration_completed` | boolean | false | Status da migração |
| `rm_migration_notice_dismissed` | boolean | false | Aviso de migração dispensado |

### Regras de Redirecionamento

#### 1. Redirecionamentos Administrativos
```php
foreach ($admin_redirections as $from => $to) {
    if ($current_path === $from) {
        wp_redirect(site_url('/' . $to));
        exit();
    }
}
```

#### 2. Redirecionamentos Específicos
- `/painel` → `/painel/settings/`
- Páginas de curso singular → Área de membros
- Dashboard do TutorLMS → Página de configurações

#### 3. Redirecionamentos de Usuários Logados
- Páginas de login/cadastro → Área de membros
- `/courses` → Área de membros

#### 4. Redirecionamentos de Usuários Não Logados
- Qualquer página (exceto exceções) → Página de login
- Exceções: páginas na lista `rm_exceptions`

### Interface Administrativa

#### Estrutura HTML
```html
<div class="rm-admin-wrap">
    <div class="rm-header">...</div>
    <div class="rm-container">
        <div class="rm-card rm-status-card">...</div>
        <div class="rm-grid">
            <div class="rm-card">...</div> <!-- Configurações -->
            <div class="rm-card">...</div> <!-- Exceções -->
        </div>
        <div class="rm-card">...</div> <!-- Redirecionamentos -->
        <div class="rm-actions">...</div>
    </div>
</div>
```

#### Componentes JavaScript
- **Validação em tempo real**: Sanitização de slugs
- **Gerenciamento de exceções**: Adicionar/remover dinamicamente
- **Notificações**: Sistema de feedback visual
- **Loading states**: Indicadores de carregamento
- **Atalhos de teclado**: Ctrl+S para salvar

### Endpoints AJAX

#### `save_redirection_settings`
- **Método**: POST
- **Parâmetros**: enabled, login_page, register_page, member_area, settings_page
- **Validação**: Nonce, permissões, sanitização
- **Resposta**: JSON success/error

#### `add_exception`
- **Método**: POST
- **Parâmetros**: page
- **Validação**: Duplicatas, sanitização
- **Resposta**: Lista atualizada de exceções

#### `remove_exception`
- **Método**: POST
- **Parâmetros**: page
- **Resposta**: Lista atualizada de exceções

### Segurança

#### Validações Implementadas
1. **Nonce verification**: Todas as requisições AJAX
2. **Capability check**: `manage_options` para todas as ações
3. **Input sanitization**: `sanitize_text_field()` em todos os inputs
4. **Direct access prevention**: `ABSPATH` check em todos os arquivos

#### Prevenção de Ataques
- **XSS**: `esc_attr()`, `esc_html()` em todas as saídas
- **CSRF**: Nonce em formulários e AJAX
- **SQL Injection**: Uso de `update_option()` e `get_option()`
- **Directory traversal**: Validação de caminhos

### Performance

#### Otimizações
1. **Carregamento condicional**: Assets apenas na página do plugin
2. **Cache de configurações**: Uso do sistema de opções do WordPress
3. **Verificações mínimas**: Early returns em verificações de segurança
4. **Lazy loading**: JavaScript carregado no footer

#### Impacto no Frontend
- **Hook priority**: `template_redirect` com prioridade 5
- **Verificações rápidas**: Elementor, admin, AJAX
- **Exit early**: Return imediato se sistema desativado

### Compatibilidade

#### WordPress
- **Versão mínima**: 5.0
- **Hooks utilizados**: Padrões do WordPress
- **APIs utilizadas**: Options API, Settings API, AJAX API

#### Plugins Testados
- ✅ TutorLMS
- ✅ Elementor
- ✅ WP Rocket
- ✅ W3 Total Cache
- ✅ LiteSpeed Cache

#### Temas
- ✅ Astra (tema pai)
- ✅ Temas filhos personalizados

### Debugging

#### Modo Debug
```php
if (defined('WP_DEBUG') && WP_DEBUG) {
    // Página de teste disponível
    // Logs detalhados
    // Arquivo test-redirections.php carregado
}
```

#### Logs Úteis
```php
error_log('RM: Redirecionamento executado - ' . $current_path);
error_log('RM: Configurações - ' . print_r($settings, true));
```

### Manutenção

#### Atualizações Futuras
1. **Backup de configurações**: Antes de atualizações
2. **Testes de regressão**: Verificar todos os redirecionamentos
3. **Migração de dados**: Script automático para mudanças de estrutura

#### Monitoramento
- Verificar logs de erro do WordPress
- Monitorar performance com Query Monitor
- Testar redirecionamentos após atualizações

### Extensibilidade

#### Hooks Personalizados
```php
// Filtro para modificar configurações
apply_filters('rm_redirection_settings', $settings);

// Ação antes do redirecionamento
do_action('rm_before_redirect', $from, $to);

// Filtro para exceções dinâmicas
apply_filters('rm_dynamic_exceptions', $exceptions);
```

#### Exemplo de Extensão
```php
// Adicionar exceção dinâmica
add_filter('rm_dynamic_exceptions', function($exceptions) {
    if (is_user_logged_in() && current_user_can('subscriber')) {
        $exceptions[] = 'area-especial';
    }
    return $exceptions;
});
```