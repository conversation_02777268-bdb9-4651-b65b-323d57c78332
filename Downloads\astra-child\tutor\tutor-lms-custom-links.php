<?php
if (!defined('ABSPATH')) {
    exit;
}

class MemberoLMS_Admin {
    private static $instance = null;

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_assets'));
        add_action('wp_ajax_save_memberolms_settings', array($this, 'save_settings'));
    }

    public function add_admin_menu() {
        add_menu_page(
            'Membero LMS',
            'Membero LMS',
            'manage_options',
            'memberolms-settings',
            array($this, 'render_settings_page'),
            'dashicons-welcome-learn-more',
            30
        );
    }

    public function enqueue_assets($hook) {
        if (strpos($hook, 'memberolms-settings') === false) {
            return;
        }

        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        wp_enqueue_style(
            'memberolms-admin-styles',
            get_stylesheet_directory_uri() . '/assets/css/memberolms-admin.css',
            array(),
            '1.0.0'
        );

        wp_enqueue_script(
            'memberolms-admin',
            get_stylesheet_directory_uri() . '/assets/js/memberolms-admin.js',
            array('jquery', 'wp-color-picker'),
            '1.0.0',
            true
        );

        wp_localize_script('memberolms-admin', 'memberoLMSAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('memberolms-settings-nonce')
        ));
    }

    private function get_opacity_value($course_id) {
        $opacity = get_post_meta($course_id, '_tutor_lms_custom_overlay_background_opacity', true);
        return $opacity === '' ? 1 : floatval($opacity);
    }

    public function render_settings_page() {
        $courses = get_posts(array(
            'post_type' => 'courses',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        ?>
        <div class="wrap memberolms-settings">
            <h1>Membero LMS - Configurações</h1>
            <p class="description">Gerencie as configurações dos seus cursos</p>

            <div class="memberolms-container">
                <?php foreach ($courses as $course): 
                    $course_id = $course->ID;
                    $redirect_url = get_post_meta($course_id, '_memberolink_redirect_url', true);
                    $custom_url = get_post_meta($course_id, '_memberolink_custom_url', true);
                    $drm_enabled = get_post_meta($course_id, '_tutor_lms_custom_overlay_enabled', true);
                    $font_size = get_post_meta($course_id, '_tutor_lms_custom_overlay_font_size', true) ?: 14;
                    $bg_color = get_post_meta($course_id, '_tutor_lms_custom_overlay_background_color', true) ?: '#000000';
                    $bg_opacity = $this->get_opacity_value($course_id);
                    $display_option = get_post_meta($course_id, '_tutor_lms_custom_overlay_display_option', true) ?: 'name_email';
                ?>
                <details class="course-summary">
                    <summary>
                        <div class="course-header">
                            <img src="<?php echo get_the_post_thumbnail_url($course_id, 'thumbnail') ?: plugins_url('assets/img/course-placeholder.png', __FILE__); ?>" 
                                 alt="<?php echo esc_attr($course->post_title); ?>" 
                                 class="course-thumbnail">
                            <h2 class="course-title"><?php echo esc_html($course->post_title); ?></h2>
                        </div>
                    </summary>

                    <div class="course-settings">
                        <form class="course-settings-form" data-course-id="<?php echo $course_id; ?>">
<!-- Links Section -->
<div class="settings-section">
    <h3>Links</h3>
    <div class="setting-group">
        <label for="memberolink_custom_url_<?php echo $course_id; ?>">Link Personalizado</label>
        <input type="text"
               id="memberolink_custom_url_<?php echo $course_id; ?>"
               name="memberolink_custom_url"
               value="<?php echo esc_attr(get_post_meta($course_id, '_memberolink_custom_url', true)); ?>"
               class="regular-text">
    </div>
    <div class="setting-group">
        <label for="memberolink_redirect_url_<?php echo $course_id; ?>">Link para compra quando conteúdo bloqueado</label>
        <input type="text"
               id="memberolink_redirect_url_<?php echo $course_id; ?>"
               name="memberolink_redirect_url"
               value="<?php echo esc_attr(get_post_meta($course_id, '_memberolink_redirect_url', true)); ?>"
               class="regular-text">
    </div>
</div>
                            
                            <!-- DRM Section -->
                            <div class="settings-section">
                                <h3>Configurações do DRM</h3>
                                <div class="setting-group">
                                    <label>
                                        <input type="checkbox" name="drm_enabled" value="1" <?php checked($drm_enabled, 1); ?>>
                                        Ativar DRM Overlay
                                    </label>
                                </div>
                                <div class="setting-group drm-settings" <?php echo !$drm_enabled ? 'style="display:none;"' : ''; ?>>
                                    <label for="font_size_<?php echo $course_id; ?>">Tamanho da Fonte:</label>
                                    <select id="font_size_<?php echo $course_id; ?>" name="font_size">
                                        <?php for ($i = 9; $i <= 20; $i++): ?>
                                            <option value="<?php echo $i; ?>" <?php selected($font_size, $i); ?>><?php echo $i; ?>px</option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                <div class="setting-group drm-settings" <?php echo !$drm_enabled ? 'style="display:none;"' : ''; ?>>
                                    <label for="background_color_<?php echo $course_id; ?>">Cor de Fundo:</label>
                                    <input type="text" id="background_color_<?php echo $course_id; ?>" name="background_color" value="<?php echo esc_attr($bg_color); ?>" class="color-field">
                                </div>
                                <div class="setting-group drm-settings" <?php echo !$drm_enabled ? 'style="display:none;"' : ''; ?>>
                                    <label for="background_opacity_<?php echo $course_id; ?>">Opacidade:</label>
                                    <input type="number" id="background_opacity_<?php echo $course_id; ?>" name="background_opacity" min="0" max="1" step="0.1" value="<?php echo number_format($bg_opacity, 1); ?>">
                                </div>
                                <div class="setting-group drm-settings" <?php echo !$drm_enabled ? 'style="display:none;"' : ''; ?>>
                                    <label for="display_option_<?php echo $course_id; ?>">Informações Exibidas:</label>
                                    <select id="display_option_<?php echo $course_id; ?>" name="display_option">
                                        <option value="name_email" <?php selected($display_option, 'name_email'); ?>>Nome e Email</option>
                                        <option value="name_email_cpf" <?php selected($display_option, 'name_email_cpf'); ?>>Nome, Email e CPF</option>
                                        <option value="name_email_cpf_whatsapp" <?php selected($display_option, 'name_email_cpf_whatsapp'); ?>>Nome, Email, CPF e WhatsApp</option>
                                        <option value="name_cpf" <?php selected($display_option, 'name_cpf'); ?>>Nome e CPF</option>
                                        <option value="name_whatsapp" <?php selected($display_option, 'name_whatsapp'); ?>>Nome e WhatsApp</option>
                                        <option value="name_only" <?php selected($display_option, 'name_only'); ?>>Apenas Nome</option>
                                        <option value="email_only" <?php selected($display_option, 'email_only'); ?>>Apenas Email</option>
                                        <option value="cpf_only" <?php selected($display_option, 'cpf_only'); ?>>Apenas CPF</option>
                                        <option value="whatsapp_only" <?php selected($display_option, 'whatsapp_only'); ?>>Apenas WhatsApp</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="setting-group submit-group">
                                <button type="submit" class="button button-primary">Salvar Configurações</button>
                            </div>
                        </form>
                    </div>
                </details>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }

public function save_settings() {
    check_ajax_referer('memberolms-settings-nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Permissão negada');
        return;
    }

    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    if (!$course_id) {
        wp_send_json_error('ID do curso inválido');
        return;
    }

    // Salva Link Personalizado
    if (isset($_POST['memberolink_custom_url'])) {
        $custom_url = sanitize_text_field($_POST['memberolink_custom_url']);
        update_post_meta($course_id, '_memberolink_custom_url', $custom_url);
    }

    // Salva Link para compra quando conteúdo bloqueado
    if (isset($_POST['memberolink_redirect_url'])) {
        $redirect_url = sanitize_text_field($_POST['memberolink_redirect_url']);
        update_post_meta($course_id, '_memberolink_redirect_url', $redirect_url);
    }

    // Outras configurações do DRM
    update_post_meta($course_id, '_tutor_lms_custom_overlay_enabled', !empty($_POST['drm_enabled']) ? 1 : 0);
    update_post_meta($course_id, '_tutor_lms_custom_overlay_font_size', intval($_POST['font_size']));
    update_post_meta($course_id, '_tutor_lms_custom_overlay_background_color', sanitize_hex_color($_POST['background_color']));
    update_post_meta($course_id, '_tutor_lms_custom_overlay_background_opacity', floatval($_POST['background_opacity']));
    update_post_meta($course_id, '_tutor_lms_custom_overlay_display_option', sanitize_text_field($_POST['display_option']));

    // Verifica se os links foram salvos corretamente
    $saved_custom_url = get_post_meta($course_id, '_memberolink_custom_url', true);
    $saved_redirect_url = get_post_meta($course_id, '_memberolink_redirect_url', true);

    wp_send_json_success('Configurações salvas com sucesso');
}
}

// Inicializa a classe
MemberoLMS_Admin::get_instance();