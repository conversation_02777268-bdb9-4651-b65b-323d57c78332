jQuery(document).ready(function($) {
    // Usando delegação de eventos para garantir que o modal funcione em todos os elementos, mesmo que carregados dinamicamente
    $('body').on('click', '.trigger-modal', function(e) {
        var userName = $(this).data('modal-user-name');
        var releaseDate = $(this).data('modal-release-date');

        if (userName && releaseDate) {
            e.preventDefault();
            $('#modalUserName').text(userName);
            $('#modalReleaseDate').text(releaseDate);
            $('#customModal').addClass('show');  // Adiciona a classe 'show' para exibir o modal
        }
    });

    // Fechar o modal ao clicar no botão de fechar
    $('.custom-close').on('click', function() {
        $('#customModal').removeClass('show');  // Remove a classe 'show' para esconder o modal
    });

    // Fechar o modal ao clicar fora dele
    $(window).on('click', function(event) {
        if ($(event.target).is('#customModal')) {
            $('#customModal').removeClass('show');  // Esconde o modal ao clicar fora dele
        }
    });
});
