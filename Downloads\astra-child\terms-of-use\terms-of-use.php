<?php
/**
 * Terms of Use System
 * 
 * @package Astra-Child
 * @subpackage Terms-of-Use
 */

if (!defined('ABSPATH')) exit;

// Importar o DomPDF
use Dompdf\Dompdf;

class Terms_Of_Use {
    /**
     * Instance única da classe
     * @var Terms_Of_Use
     */
    private static $instance = null;

    /**
     * Opções padrão do plugin
     * @var array
     */
    private $default_options = [
        'ativo' => '0',
        'texto_termo' => '',
        'cor_texto' => '#000000',
        'cor_fundo' => '#ffffff',
        'cor_botao_aceitar' => '#4CAF50',
        'cor_botao_sair' => '#f44336',
        'texto_botao_aceitar' => 'Sim, eu aceito',
        'texto_botao_sair' => 'Sair',
        'ipinfo_token' => ''
    ];
    
    /**
     * Retorna a instância única da classe
     * @return Terms_Of_Use
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Construtor privado
     */
    private function __construct() {
        $this->setup_hooks();
        $this->register_shortcodes();
    }
    
    /**
     * Configura os hooks do WordPress
     */
    private function setup_hooks() {
        // Admin
        add_action('admin_menu', array($this, 'setup_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Frontend
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'exibir_termo'));
        
        // Ajax
        add_action('wp_ajax_registrar_aceite', array($this, 'registrar_aceite'));
        add_action('wp_ajax_nopriv_registrar_aceite', array($this, 'registrar_aceite'));
        
        // Instalação
        add_action('after_switch_theme', array($this, 'criar_tabelas'));
        add_action('after_switch_theme', array($this, 'setup_default_options'));
        
        add_action('after_setup_theme', function() {
        if (current_user_can('manage_options')) {
            Terms_Of_Use::get_instance()->criar_tabelas();
        }
        });
        
        add_action('wp_ajax_baixar_termo', array($this, 'gerar_termo_para_download'));
        add_filter('litespeed_cache_control', array($this, 'add_litespeed_cache_exclusion'));
        add_action('show_user_profile', array($this, 'exibir_registros_termo_no_perfil'));
        add_action('edit_user_profile', array($this, 'exibir_registros_termo_no_perfil'));

    }
    

public function exibir_registros_termo_no_perfil($user) {
    global $wpdb;

    // Obter o ID do usuário
    $user_id = $user->ID;

    // Query para buscar os registros do termo para o usuário
    $registros = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}termo_aceites WHERE user_id = %d ORDER BY data_aceite DESC",
            $user_id
        )
    );

    ?>
    <h2>Registros de Aceite do Termo</h2>
    <div class="termo-card">
        <div class="termo-table-container">
            <table class="termo-table">
                <thead>
                    <tr>
                        <th>Usuário</th>
                        <th>CPF/CNPJ</th>
                        <th>WhatsApp</th>
                        <th>E-mail</th>
                        <th>IP</th>
                        <th>Data de Aceite</th>
                        <th>Navegador</th>
                        <th>Versão do Termo</th>
                        <th>Baixar</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($registros)): ?>
                        <?php foreach ($registros as $registro): 
                            $user_info = $this->get_user_info($registro->user_id);
                            if (!$user_info) continue;
                        ?>
                            <tr>
                                <td><?php echo esc_html($user_info['name']); ?></td>
                                <td>
                                    <?php if ($user_info['cpf']): ?>
                                        <?php echo esc_html($user_info['cpf']); ?>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user_info['whatsapp']): ?>
                                        <?php echo esc_html($user_info['whatsapp']); ?>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($user_info['email']); ?></td>
                                <td>
                                    <?php echo $registro->ip ? esc_html($registro->ip) : '<span class="badge badge-warning">Não informado</span>'; ?>
                                </td>
                                <td><?php echo esc_html(date('d/m/Y H:i:s', strtotime($registro->data_aceite))); ?></td>
                                <td class="termo-browser-col">
                                    <?php if (!empty($registro->navegador)): ?>
                                        <span class="termo-tooltip" title="<?php echo esc_attr($registro->navegador); ?>">
                                            <?php echo esc_html(substr($registro->navegador, 0, 30)) . '...'; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($registro->versao_termo); ?></td>
                                <td>
                                    <a href="<?php echo admin_url('admin-ajax.php?action=baixar_termo&registro_id=' . $registro->id); ?>" class="button">
                                        Baixar Termo
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="termo-empty-message">Nenhum registro encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
    </div>
    <?php
}


     public function gerar_termo_para_download() {
        if (!current_user_can('manage_options')) {
            wp_die('Você não tem permissão para acessar este recurso.');
        }
    
        global $wpdb;
    
        // Sanitizar e verificar o registro ID
        $registro_id = isset($_GET['registro_id']) ? intval($_GET['registro_id']) : 0;
    
        if (!$registro_id) {
            wp_die('ID do registro inválido.');
        }
        // Obter o registro do banco de dados
        $registro = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}termo_aceites WHERE id = %d",
            $registro_id
        ));
    
        if (!$registro) {
            wp_die('Registro não encontrado.');
        }
    
        // Obter informações do usuário
        $user_id = $registro->user_id;
        $user_info = $this->get_user_info($user_id);
    
        if (!$user_info) {
            wp_die('Informações do usuário não encontradas.');
        }
    
        // Obter dados de geolocalização
        $geo_info = $this->get_user_geolocation();
    
        // Detectar o dispositivo do usuário
        $user_agent_info = $this->get_user_agent_info();
    
        // Normaliza a versão do termo e busca no histórico
        $versao_termo = str_replace(',', '.', $registro->versao_termo);
        $termo_historico = $wpdb->get_row($wpdb->prepare(
            "SELECT texto FROM {$wpdb->prefix}termo_historico 
             WHERE REPLACE(versao, ',', '.') = %s",
            $versao_termo
        ));
    
        if (!$termo_historico) {
            error_log('Versão do termo não encontrada: ' . $versao_termo);
            wp_die('Texto do termo para a versão especificada não encontrado.');
        }
    
        // Substituir placeholders no texto do termo
        $texto_termo = strtr($termo_historico->texto, [
            '[titulo_site]' => get_bloginfo('name'),
            '[nome_usuario]' => $user_info['name'] ?? 'Não disponível',
            '[cpf_usuario]' => $user_info['cpf'] ?? 'Não disponível',
            '[email_usuario]' => $user_info['email'] ?? 'Não disponível',
            '[cidade_usuario]' => $geo_info['city'] ?? 'Não disponível',
            '[estado_usuario]' => $geo_info['region'] ?? 'Não disponível',
            '[pais_usuario]' => $geo_info['country'] ?? 'Não disponível',
            '[ip_usuario]' => $registro->ip,
            '[navegador_usuario]' => $registro->navegador,
            '[dispositivo_usuario]' => $user_agent_info['device'] ?? 'Não disponível',
            '[data_aceite]' => date('d/m/Y H:i:s', strtotime($registro->data_aceite))
        ]);
    
        // Carregar o autoloader do Composer
        require_once __DIR__ . '/vendor/autoload.php';
    
        // Inicializar o DomPDF
        $dompdf = new Dompdf();
        $dompdf->loadHtml('<h1>Termo de Aceite</h1><p>' . nl2br($texto_termo) . '</p>');
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
    
        // Nome formatado para o arquivo PDF
        $user_display_name = sanitize_file_name($user_info['name'] ?? 'usuario');
        $filename = "termo_uso_{$user_display_name}_{$registro_id}.pdf";
    
        // Configurar cabeçalhos para download
        header('Content-Description: File Transfer');
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
    
        // Enviar o PDF para o navegador
        echo $dompdf->output();
        exit;
    }


    /**
     * Configura as opções padrão
     */
    public function setup_default_options() {
        $existing_options = get_option('termo_uso_options', array());
        $options = wp_parse_args($existing_options, $this->default_options);
        update_option('termo_uso_options', $options);
    }
    
    /**
     * Registra os shortcodes
     */
    private function register_shortcodes() {
        $shortcodes = array(
            'nome_usuario' => 'shortcode_nome_usuario',
            'whatsapp_usuario' => 'shortcode_whatsapp_usuario',
            'cpf_usuario' => 'shortcode_cpf_usuario',
            'email_usuario' => 'shortcode_email_usuario',
            'ip_usuario' => 'shortcode_ip_usuario',
            'navegador_usuario' => 'shortcode_navegador_usuario',
            'dispositivo_usuario' => 'shortcode_dispositivo_usuario',
            'sistema_usuario' => 'shortcode_sistema_usuario',
            'titulo_site' => 'shortcode_titulo_site',
            'url_site' => 'shortcode_url_site',
            'data_hora' => 'shortcode_data_hora'
        );
        
        foreach ($shortcodes as $tag => $function) {
            add_shortcode($tag, array($this, $function));
        }
    }
    

    /**
     * Configura o ip do cliente
     */
    
    private function get_client_ip() {
    $ip = '';
    
    if (isset($_SERVER['HTTP_CF_CONNECTING_IP'])) {
        $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
    }
    elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
        $ip = $_SERVER['HTTP_X_REAL_IP'];
    }
    elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $ip = trim($ips[0]);
    }
    elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }

    return filter_var($ip, FILTER_VALIDATE_IP) ? $ip : $_SERVER['REMOTE_ADDR'];
    }


    private function get_user_agent_info() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        
        // Detecta o navegador
        $browser = "Desconhecido";
        if (preg_match('/Firefox/i', $user_agent)) {
            $browser = "Firefox";
        } elseif (preg_match('/Edge|Edg/i', $user_agent)) {
            $browser = "Edge";
        } elseif (preg_match('/Chrome/i', $user_agent)) {
            $browser = "Chrome";
        } elseif (preg_match('/Safari/i', $user_agent)) {
            $browser = "Safari";
        } elseif (preg_match('/Opera|OPR/i', $user_agent)) {
            $browser = "Opera";
        } elseif (preg_match('/MSIE|Trident/i', $user_agent)) {
            $browser = "Internet Explorer";
        }
    
        // Detecta o dispositivo
        $device = "Desktop";
        if (preg_match('/(tablet|ipad|playbook)|(android(?!.*(mobi|opera mini)))/i', strtolower($user_agent))) {
            $device = "Tablet";
        } elseif (preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|android|iemobile)/i', strtolower($user_agent))) {
            $device = "Mobile";
        }
    
        // Detecta o sistema operacional
        $os = "Desconhecido";
        if (preg_match('/windows/i', $user_agent)) {
            $os = "Windows";
        } elseif (preg_match('/macintosh|mac os x/i', $user_agent)) {
            $os = "Mac OS";
        } elseif (preg_match('/linux/i', $user_agent)) {
            $os = "Linux";
        } elseif (preg_match('/android/i', $user_agent)) {
            $os = "Android";
        } elseif (preg_match('/iphone|ipad|ipod/i', $user_agent)) {
            $os = "iOS";
        }
    
        return array(
            'browser' => $browser,
            'device' => $device,
            'os' => $os
        );
    }
    
    /**
     * Configura o menu do admin
     */
    public function setup_admin_menu() {
        add_menu_page(
            'Termo de Uso',
            'Termo de Uso',
            'manage_options',
            'termo-uso',
            array($this, 'admin_page'),
            'dashicons-welcome-write-blog',
            30
        );
        
        add_submenu_page(
            'termo-uso',
            'Configurações',
            'Configurações',
            'manage_options',
            'termo-uso',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'termo-uso',
            'Registros de Aceite',
            'Registros de Aceite',
            'manage_options',
            'termo-uso-registros',
            array($this, 'registros_page')
        );
    }
    
public function enqueue_admin_scripts($hook) {
    // Verifica se estamos na página de edição de perfil do usuário ou no menu do Termo de Uso
    if (strpos($hook, 'termo-uso') !== false || $hook === 'profile.php' || $hook === 'user-edit.php') {
        $css_file = get_stylesheet_directory() . '/terms-of-use/terms-of-use.css';
        
        wp_enqueue_style(
            'termo-uso-admin',
            get_stylesheet_directory_uri() . '/terms-of-use/terms-of-use.css',
            array(),
            file_exists($css_file) ? filemtime($css_file) : '1.0.0'
        );
    }
}

    

    /**
     * Enqueue scripts e estilos do frontend
     */
    public function enqueue_scripts() {
        if ($this->precisa_mostrar_termo()) {
            // Define os caminhos dos arquivos
            $css_file = get_stylesheet_directory() . '/terms-of-use/terms-of-use.css';
            $js_file = get_stylesheet_directory() . '/terms-of-use/terms-of-use.js';
            
            // CSS
            wp_enqueue_style(
                'termo-uso-style',
                get_stylesheet_directory_uri() . '/terms-of-use/terms-of-use.css',
                array(),
                file_exists($css_file) ? filemtime($css_file) : '1.0.0'
            );
            
            // SweetAlert2 Script
            wp_enqueue_script(
                'sweetalert2',
                'https://cdn.jsdelivr.net/npm/sweetalert2@11',
                array('jquery'),
                null,
                true
            );
    
            // JavaScript principal do termo de uso
            wp_enqueue_script(
                'termo-uso-script',
                get_stylesheet_directory_uri() . '/terms-of-use/terms-of-use.js',
                array('jquery', 'sweetalert2'), // Certifica que SweetAlert2 é carregado antes
                file_exists($js_file) ? filemtime($js_file) : '1.0.0',
                true
            );
            
            // Localização do script
            wp_localize_script('termo-uso-script', 'termoUsoAjax', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'logout_url' => wp_logout_url(),
                'options' => array(
                    'cor_texto' => get_option('termo_uso_options')['cor_texto'] ?? '#000000',
                    'cor_fundo' => get_option('termo_uso_options')['cor_fundo'] ?? '#ffffff',
                    'cor_botao_aceitar' => get_option('termo_uso_options')['cor_botao_aceitar'] ?? '#4CAF50',
                    'cor_botao_sair' => get_option('termo_uso_options')['cor_botao_sair'] ?? '#f44336'
                )
            ));
        }
    }

    
    /**
     * Cria as tabelas no banco de dados
     */
    public function criar_tabelas() {
        global $wpdb;
    
        $charset_collate = $wpdb->get_charset_collate();
    
        // SQL para a tabela de registros de aceite
        $sql_aceites = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}termo_aceites (
            id BIGINT(20) NOT NULL AUTO_INCREMENT,
            user_id BIGINT(20) NOT NULL,
            ip VARCHAR(45) NOT NULL,
            navegador VARCHAR(255) NOT NULL,
            data_aceite DATETIME NOT NULL,
            versao_termo VARCHAR(10) NOT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY data_aceite (data_aceite)
        ) $charset_collate;";
    
        // SQL para a tabela de histórico de termos
        $sql_historico = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}termo_historico (
            id BIGINT(20) NOT NULL AUTO_INCREMENT,
            versao VARCHAR(10) NOT NULL,
            texto LONGTEXT NOT NULL,
            data_criacao DATETIME NOT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY versao (versao)
        ) $charset_collate;";
    
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
        // Executa os comandos SQL
        dbDelta($sql_aceites);
        dbDelta($sql_historico);
    }


    
    /**
     * Página de configurações do admin
     */
    public function admin_page() {
        if (isset($_POST['save_termo'])) {
            $this->save_settings();
        }
        
        $options = get_option('termo_uso_options', array());
        include get_stylesheet_directory() . '/terms-of-use/templates-terms-of-use.php';
    }


    /**
     * Salva as configurações do termo
     */
    private function save_settings() {
        if (!current_user_can('manage_options')) return;
    
        global $wpdb;
    
        // Obtenha as opções atuais
        $current_options = get_option('termo_uso_options', array());
    
        // Pegue os valores enviados no formulário
        $novo_texto_termo = wp_kses_post($_POST['texto_termo']);
    
        // Trata a versão do termo adequadamente
        if (!isset($_POST['versao_termo']) || empty($_POST['versao_termo'])) {
            wp_die('A versão do termo é obrigatória.');
        }
    
        $nova_versao_termo = str_replace(',', '.', $_POST['versao_termo']);
        $nova_versao_termo = number_format((float)$nova_versao_termo, 1, '.', ''); // Formata como "1.0", "1.1", etc.
    
        // Debug
        error_log('Nova versão do termo (após processamento): ' . $nova_versao_termo);
    
        // Verifique a versão atual no histórico
        $versao_historica = $wpdb->get_var("
            SELECT MAX(CAST(REPLACE(versao, ',', '.') AS DECIMAL(10,2))) 
            FROM {$wpdb->prefix}termo_historico
        ");
    
        if (!$versao_historica || version_compare($nova_versao_termo, $versao_historica, '>')) {
            // Adiciona o termo ao histórico
            $resultado = $wpdb->insert(
                "{$wpdb->prefix}termo_historico",
                array(
                    'versao' => $nova_versao_termo,
                    'texto' => $novo_texto_termo,
                    'data_criacao' => current_time('mysql'),
                ),
                array('%s', '%s', '%s')
            );
    
            if ($resultado === false) {
                error_log("Erro ao salvar no histórico: " . $wpdb->last_error);
                wp_die('Erro ao salvar no histórico do termo.');
            }
    
            error_log('Resultado da inserção no histórico: ' . var_export($resultado, true));
        } else {
            wp_die('A nova versão do termo deve ser maior que a versão anterior.');
        }
    
        // Atualiza as opções do termo de uso
        $options = array(
            'texto_termo' => $novo_texto_termo,
            'versao_termo' => $nova_versao_termo, // Mantém o formato "1.0"
            'cor_texto' => sanitize_hex_color($_POST['cor_texto']),
            'cor_fundo' => sanitize_hex_color($_POST['cor_fundo']),
            'cor_botao_aceitar' => sanitize_hex_color($_POST['cor_botao_aceitar']),
            'cor_botao_sair' => sanitize_hex_color($_POST['cor_botao_sair']),
            'texto_botao_aceitar' => sanitize_text_field($_POST['texto_botao_aceitar']),
            'texto_botao_sair' => sanitize_text_field($_POST['texto_botao_sair']),
            'ipinfo_token' => sanitize_text_field($_POST['ipinfo_token']),
            'ativo' => isset($_POST['ativo']) ? '1' : '0',
        );
    
        // Debug
        error_log('Opções sendo salvas: ' . print_r($options, true));
    
        update_option('termo_uso_options', $options);
    }



    /**
     * Página de registros de aceite
     */
    public function registros_page() {
        global $wpdb;
    
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
    
        // Verificar se existe um critério de busca
        $search = isset($_GET['search']) ? trim($_GET['search']) : null;
    
        // Construir a query de registros
        $query = "SELECT tu.*, u.ID as user_id, u.user_email, u.user_login 
                  FROM {$wpdb->prefix}termo_aceites tu 
                  LEFT JOIN {$wpdb->prefix}users u ON tu.user_id = u.ID";
    
        // Adicionar condição de filtro se um critério foi especificado
        if ($search) {
            if (is_numeric($search)) {
                // Buscar por ID
                $query .= $wpdb->prepare(" WHERE tu.user_id = %d", intval($search));
            } else {
                // Buscar por email ou nome de usuário
                $query .= $wpdb->prepare(" WHERE u.user_email LIKE %s OR u.user_login LIKE %s", "%{$search}%", "%{$search}%");
            }
        }
    
        $query .= " ORDER BY tu.data_aceite DESC LIMIT %d OFFSET %d";
    
        // Executar a query com paginação
        $registros = $wpdb->get_results($wpdb->prepare($query, $per_page, $offset));
    
        // Contar o total de registros
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}termo_aceites tu 
                        LEFT JOIN {$wpdb->prefix}users u ON tu.user_id = u.ID";
    
        if ($search) {
            if (is_numeric($search)) {
                $total_query .= $wpdb->prepare(" WHERE tu.user_id = %d", intval($search));
            } else {
                $total_query .= $wpdb->prepare(" WHERE u.user_email LIKE %s OR u.user_login LIKE %s", "%{$search}%", "%{$search}%");
            }
        }
    
        $total = $wpdb->get_var($total_query);
    
        // Calcular o número total de páginas
        $total_pages = ceil($total / $per_page);
    
        // Incluir o template para exibir os registros
        include get_stylesheet_directory() . '/terms-of-use/templates-terms-of-use-registros.php';
    }


    /**
     * Registra o aceite do termo
     */
    public function registrar_aceite() {
        if (!is_user_logged_in()) {
            wp_send_json_error('Usuário não está logado');
            return;
        }
    
        global $wpdb;
    
        $user_id = get_current_user_id();
        $ip = $this->get_client_ip();
        $navegador = $_SERVER['HTTP_USER_AGENT'];
    
        // Obtém a versão atual do termo
        $options = get_option('termo_uso_options', array());
        $versao_atual = isset($options['versao_termo']) 
            ? number_format((float)$options['versao_termo'], 1, '.', '') 
            : '1.0';
    
        // Debug da versão atual
        error_log('Tentando registrar aceite para versão: ' . $versao_atual);
    
        // Verifica se o usuário já aceitou esta versão do termo
        $registro_existente = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}termo_aceites 
             WHERE user_id = %d AND versao_termo = %s",
            $user_id,
            $versao_atual
        ));
    
        if ($registro_existente > 0) {
            wp_send_json_error('Você já aceitou a versão atual do termo.');
            return;
        }
    
        // Verifica se a versão atual está no histórico
        $versao_historico = $wpdb->get_var($wpdb->prepare(
            "SELECT versao FROM {$wpdb->prefix}termo_historico WHERE versao = %s",
            $versao_atual
        ));
    
        if (!$versao_historico) {
            error_log('Versão não encontrada no histórico: ' . $versao_atual);
    
            // Debug - listar todas as versões no histórico
            $todas_versoes = $wpdb->get_col("SELECT versao FROM {$wpdb->prefix}termo_historico");
            error_log('Versões disponíveis no histórico: ' . print_r($todas_versoes, true));
    
            wp_send_json_error('Erro: A versão do termo não está registrada no histórico.');
            return;
        }
    
        // Insere o registro no banco de dados usando a versão exata
        $result = $wpdb->insert(
            "{$wpdb->prefix}termo_aceites",
            array(
                'user_id' => $user_id,
                'ip' => $ip,
                'navegador' => $navegador,
                'data_aceite' => current_time('mysql'),
                'versao_termo' => $versao_historico
            ),
            array('%d', '%s', '%s', '%s', '%s')
        );
    
        if ($result === false) {
            error_log('Erro ao inserir registro no banco: ' . $wpdb->last_error);
            wp_send_json_error('Erro ao registrar o termo.');
            return;
        }
    
        // Atualiza o meta do usuário com a versão aceita
        update_user_meta($user_id, 'termo_aceito', $versao_historico);
    
        // Purga o cache, se necessário
        do_action('litespeed_purge_all');
    
        wp_send_json_success('Termo aceito com sucesso.');
    }



    /**
     * Exibe o termo no frontend
     */
    public function exibir_termo() {
        if (!$this->precisa_mostrar_termo()) {
            return;
        }
    
        $options = get_option('termo_uso_options', array());
        $ip = $this->get_client_ip();
        $navegador = $_SERVER['HTTP_USER_AGENT'];
        $data = current_time('d/m/Y H:i:s');
    
        // Obter localização
        $geo_info = $this->get_user_geolocation();
    
        // Processar o texto do termo antes de exibir
        $texto_termo = $options['texto_termo'] ?? '';
        $texto_termo = strtr($texto_termo, [
            '[cidade_usuario]' => $geo_info['city'] ?? 'Não disponível',
            '[estado_usuario]' => $geo_info['region'] ?? 'Não disponível',
            '[pais_usuario]' => $geo_info['country'] ?? 'Não disponível'
        ]);
        
         // Atualizar as opções com o texto processado
        $options['texto_termo'] = $texto_termo;
    
    
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        

        // Renderiza o termo
        echo '<div class="termo-backdrop"></div>';
        include get_stylesheet_directory() . '/terms-of-use/template-termo-frontend.php';
    }

    
    /**
     * Verifica se o usuário aceitou corretamente o termo de uso.
     * Caso contrário, força o aceite novamente.
     */
    private function verificar_e_forcar_aceite() {
        if (!is_user_logged_in()) {
            error_log('Usuário não está logado.');
            return true; // Força o aceite se o usuário não estiver logado
        }
    
        global $wpdb;
        $user_id = get_current_user_id();
    
        // Obtém a versão atual do termo das opções
        $options = get_option('termo_uso_options', array());
        $versao_atual = isset($options['versao_termo']) 
            ? number_format((float)$options['versao_termo'], 1, '.', '') 
            : '1.0';
    
        // Verifica o último registro de aceite no banco de dados
        $ultimo_aceite = $wpdb->get_var($wpdb->prepare(
            "SELECT versao_termo FROM {$wpdb->prefix}termo_aceites 
             WHERE user_id = %d 
             ORDER BY data_aceite DESC, id DESC 
             LIMIT 1",
            $user_id
        ));
    
        if (!$ultimo_aceite) {
            error_log("Nenhum aceite encontrado para o usuário ID: $user_id. Forçando aceite.");
            return true;
        }
    
        // Normaliza as versões para comparação
        $versao_atual_normalizada = number_format((float)$versao_atual, 1, '.', '');
        $ultimo_aceite_normalizado = number_format((float)$ultimo_aceite, 1, '.', '');
    
        // Debug para acompanhamento
        error_log("Versão atual configurada: $versao_atual_normalizada");
        error_log("Último aceite encontrado: $ultimo_aceite_normalizado");
    
        // Compara as versões usando version_compare
        if (version_compare($ultimo_aceite_normalizado, $versao_atual_normalizada, '<')) {
            error_log("Versão aceita ($ultimo_aceite_normalizado) é mais antiga que a versão atual ($versao_atual_normalizada). Forçando aceite.");
            return true;
        }
    
        return false; // Não precisa forçar o aceite
    }



    /**
     * Verifica se precisa mostrar o termo
     */
    private function precisa_mostrar_termo() {
              // Se for administrador, não mostra o termo
        if (current_user_can('administrator')) {
            return false;
        }
        
        $options = get_option('termo_uso_options', array());
        $result = is_user_logged_in() &&
                  isset($options['ativo']) &&
                  $options['ativo'] === '1' &&
                  $this->verificar_e_forcar_aceite();
    
        error_log('Precisa mostrar termo: ' . ($result ? 'Sim' : 'Não'));
        return $result;
    }

    
    /**
     * Obtém informações do usuário
     */
    private function get_user_info($user_id) {
        $user_data = get_userdata($user_id);
        if (!$user_data) return false;
        
        return array(
            'cpf' => function_exists('get_user_cpf_cnpj') ? get_user_cpf_cnpj($user_id) : get_user_meta($user_id, 'cpf', true),
            'whatsapp' => function_exists('get_user_whatsapp') ? get_user_whatsapp($user_id) : get_user_meta($user_id, 'whatsapp', true),
            'name' => $user_data->display_name,
            'email' => $user_data->user_email
        );
    }
    
    /**
     * Shortcodes
     */
    public function shortcode_nome_usuario() {
        $user = wp_get_current_user();
        return $user->display_name;
    }
    
    public function shortcode_whatsapp_usuario() {
        if (function_exists('get_user_whatsapp')) {
            return get_user_whatsapp();
        }
        return get_user_meta(get_current_user_id(), 'whatsapp', true);
    }
    
    public function shortcode_cpf_usuario() {
        if (function_exists('get_user_cpf_cnpj')) {
            return get_user_cpf_cnpj();
        }
        return get_user_meta(get_current_user_id(), 'cpf', true);
    }
    
    public function shortcode_email_usuario() {
        $user = wp_get_current_user();
        return $user->user_email;
    }
    
    public function shortcode_ip_usuario() {
        return $this->get_client_ip();
    }
    
    public function shortcode_navegador_usuario() {
        $info = $this->get_user_agent_info();
        return $info['browser'];
    }
    
    public function shortcode_dispositivo_usuario() {
        $info = $this->get_user_agent_info();
        return $info['device'];
    }
    
    public function shortcode_sistema_usuario() {
        $info = $this->get_user_agent_info();
        return $info['os'];
    }
    
    public function shortcode_cidade_usuario() {
        $geo_data = $this->get_user_geolocation();
        return $geo_data['city'];
    }
    
    public function shortcode_estado_usuario() {
        $geo_data = $this->get_user_geolocation();
        return $geo_data['region'];
    }
    
    public function shortcode_pais_usuario() {
        $geo_data = $this->get_user_geolocation();
        return $geo_data['country'];
    }
    
    public function shortcode_titulo_site() {
    return get_bloginfo('name');
    }
    
    public function shortcode_url_site() {
        return get_bloginfo('url');
    }
    
    public function shortcode_data_hora() {
        return current_time('d/m/Y H:i:s');
    }
        
     /**
     * Obtém o token do IPInfo
     */
    private function get_ipinfo_token() {
        $options = get_option('termo_uso_options', array());
        return !empty($options['ipinfo_token']) ? $options['ipinfo_token'] : null;
    }

    /**
     * Cache de geolocalização
     */
    private function get_geo_cache_key($ip) {
        return 'termo_geo_' . md5($ip);
    }

    private function get_cached_geo_data($ip) {
        return get_transient($this->get_geo_cache_key($ip));
    }

    private function set_geo_cache($ip, $data) {
        set_transient($this->get_geo_cache_key($ip), $data, DAY_IN_SECONDS);
    }
    
    
    /**
     * Obtém dados de geolocalização do usuário
     */
    private function get_user_geolocation() {
        try {
            $ip = $this->get_client_ip();
            $cached_data = $this->get_cached_geo_data($ip);
    
            // Verifica se há cache disponível
            if ($cached_data !== false) {
                return $cached_data;
            }
    
            $options = get_option('termo_uso_options', array());
            $ipinfo_token = !empty($options['ipinfo_token']) ? $options['ipinfo_token'] : '';
    
            if (empty($ipinfo_token)) {
                throw new Exception('Token IPInfo não configurado');
            }
    
            // Requisição à API IPInfo
            $response = wp_remote_get("https://ipinfo.io/{$ip}/json?token={$ipinfo_token}", array(
                'timeout' => 15,
                'sslverify' => true
            ));
    
            if (is_wp_error($response)) {
                throw new Exception('Erro na requisição IPInfo: ' . $response->get_error_message());
            }
    
            $data = json_decode(wp_remote_retrieve_body($response), true);
    
            if (empty($data) || isset($data['error'])) {
                throw new Exception('Dados inválidos retornados pela API IPInfo.');
            }
    
            // Salva os dados no cache
            $this->set_geo_cache($ip, $data);
    
            return array(
                'city' => $data['city'] ?? 'Não disponível',
                'region' => $data['region'] ?? 'Não disponível',
                'country' => $data['country'] ?? 'Não disponível',
                'ip' => $ip
            );
    
        } catch (Exception $e) {
            error_log('Erro ao obter dados de geolocalização: ' . $e->getMessage());
            return array(
                'city' => 'Não disponível',
                'region' => 'Não disponível',
                'country' => 'Não disponível',
                'ip' => $this->get_client_ip(),
                'error' => $e->getMessage()
            );
        }
    }


}



// Inicializar
Terms_Of_Use::get_instance();