jQuery(document).ready(function ($) {


    // Verifique se o localStorage já possui o valor de enable_comments, caso contrário, defina como 'no'
    if (localStorage.getItem('enable_comments') === null) {
        localStorage.setItem('enable_comments', 'no');

    }

    // Função para gravar o valor no localStorage ao clicar no banner do carrossel
    $('.trigger-carousel').on('click', function (e) {
        var enableComments = $(this).data('enable-comments');


        if (enableComments !== undefined) {
            localStorage.setItem('enable_comments', enableComments);
            toggleCommentsSection(); // Chama a função imediatamente para atualizar a exibição
        }
    });

    // Função para esconder ou mostrar a seção de comentários ou a aba de comentários
    function toggleCommentsSection() {
        var storedEnableComments = localStorage.getItem('enable_comments');

        // Ocultar ou mostrar aba de comentários específica
        if (storedEnableComments === 'no') {
            var commentsTab = $('a[data-tutor-query-value="comments"]'); // Ajuste o seletor conforme necessário
            console.log('Comments Tab Found:', commentsTab.length > 0);
            if (commentsTab.length > 0) {
                commentsTab.addClass('hide-comments');
                console.log('Comments Tab Class hide-comments Added');
            } else {
                console.log('Comments Tab not found');
            }

            // Ocultar a seção de comentários geral, se existir
            $('.tutor-course-spotlight-comments').hide();

        } else if (storedEnableComments === 'yes') {
            // Mostrar aba de comentários específica
            var commentsTab = $('a[data-tutor-query-value="comments"]');
            if (commentsTab.length > 0) {
                commentsTab.removeClass('hide-comments');

            }

            // Mostrar a seção de comentários geral, se existir
            $('.tutor-course-spotlight-comments').show();

        }
    }

    // Chame a função para definir o estado da seção de comentários ao carregar a página
    $(window).on('load', function () {

        // Usar um pequeno atraso para garantir que o DOM esteja completamente carregado
        setTimeout(function() {
            toggleCommentsSection(); // Verifique o estado ao carregar a página
        }, 10); // Ajuste o tempo de atraso conforme necessário
    });
});
