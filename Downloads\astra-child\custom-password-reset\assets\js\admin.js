/**
 * Custom Password Reset Admin JavaScript
 */
(function($) {
    'use strict';

    class CPRAdmin {
        constructor() {
            this.init();
        }

        init() {
            this.setupColorPickers();
            this.setupTabs();
            this.setupConditionalFields();
            this.setupMediaUploader();
            this.setupFormHandling();
            // Preview removido - apenas link simples
            this.setupKeyboardNavigation();
        }

        setupColorPickers() {
            // Inicializar color pickers do WordPress
            if ($.fn.wpColorPicker) {
                $('.cpr-color-picker').wpColorPicker({
                    change: () => {
                        this.updatePreview();
                    },
                    clear: () => {
                        this.updatePreview();
                    }
                });
            }
        }

        setupTabs() {
            // Navegação por abas
            $('.cpr-nav-tabs .nav-tab').on('click', (e) => {
                e.preventDefault();
                
                const $tab = $(e.currentTarget);
                const tabId = $tab.data('tab');
                
                this.switchTab(tabId);
            });

            // Carregar aba da URL
            this.loadTabFromHash();
            
            // Atualizar URL quando aba mudar
            $(window).on('hashchange', () => {
                this.loadTabFromHash();
            });
        }

        switchTab(tabId) {
            // Atualizar abas ativas
            $('.nav-tab').removeClass('nav-tab-active');
            $(`.nav-tab[data-tab="${tabId}"]`).addClass('nav-tab-active');
            
            // Mostrar conteúdo da aba
            $('.cpr-tab-content').hide();
            $(`#tab-${tabId}`).show();
            
            // Atualizar URL
            window.location.hash = tabId;
            
            // Trigger resize para color pickers
            $(window).trigger('resize');
        }

        loadTabFromHash() {
            const hash = window.location.hash.substring(1);
            if (hash && $(`.nav-tab[data-tab="${hash}"]`).length) {
                this.switchTab(hash);
            }
        }

        setupConditionalFields() {
            const toggleFields = () => {
                $('.cpr-conditional').each(function() {
                    const $row = $(this);
                    const condition = $row.data('condition');
                    const value = $row.data('value');
                    const $field = $(`#${condition}`);
                    
                    let shouldShow = false;
                    
                    if ($field.is(':checkbox')) {
                        shouldShow = ($field.is(':checked') && value == '1') || 
                                   (!$field.is(':checked') && value == '0');
                    } else {
                        shouldShow = $field.val() == value;
                    }
                    
                    if (shouldShow) {
                        $row.addClass('show');
                    } else {
                        $row.removeClass('show');
                    }
                });
            };
            
            // Executar ao carregar e quando campos mudarem
            toggleFields();
            $('input, select, textarea').on('change input', toggleFields);
            

        }



        setupMediaUploader() {
            $('.cpr-upload-button').on('click', (e) => {
                e.preventDefault();
                
                const $button = $(e.currentTarget);
                const targetField = $button.data('target');
                
                // Verificar se wp.media está disponível
                if (typeof wp === 'undefined' || !wp.media) {
                    alert('Media uploader não está disponível.');
                    return;
                }
                
                const mediaUploader = wp.media({
                    title: 'Selecionar Logo',
                    button: {
                        text: 'Usar esta imagem'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });
                
                mediaUploader.on('select', () => {
                    const attachment = mediaUploader.state().get('selection').first().toJSON();
                    $(`#${targetField}`).val(attachment.url).trigger('change');
                    this.updatePreview();
                });
                
                mediaUploader.open();
            });
        }

        setupFormHandling() {
            // Salvar configurações
            $('#cpr-settings-form').on('submit', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
            
            // Restaurar padrões
            $('#cpr-reset-settings').on('click', () => {
                this.resetSettings();
            });
            
            // Auto-save em mudanças
            let saveTimeout;
            $('input, select, textarea').on('change input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.autoSave();
                }, 2000);
            });
        }

        saveSettings() {
            const $form = $('#cpr-settings-form');
            const $button = $('#cpr-save-settings');
            const $status = $('#cpr-status');
            
            $button.prop('disabled', true).find('.dashicons').addClass('spin');
            
            // Preparar dados do formulário
            let formData = $form.serializeArray();
            
            // Adicionar ação
            formData.push({
                name: 'action',
                value: 'cpr_save_settings'
            });
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', response.data);
                        this.updatePreview();
                    } else {
                        this.showStatus('error', response.data || 'Erro ao salvar configurações');
                    }
                },
                error: () => {
                    this.showStatus('error', 'Erro de conexão');
                },
                complete: () => {
                    $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                }
            });
        }

        autoSave() {
            const $form = $('#cpr-settings-form');
            
            // Preparar dados do formulário
            const formData = $form.serialize() + '&action=cpr_save_settings&auto_save=1';
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', 'Salvo automaticamente', 1000);
                    }
                }
            });
        }

        resetSettings() {
            if (!confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
                return;
            }
            
            const $button = $('#cpr-reset-settings');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'cpr_reset_settings',
                    nonce: $('input[name="nonce"]').val()
                },
                success: (response) => {
                    if (response.success) {
                        this.showStatus('success', response.data);
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        this.showStatus('error', response.data || 'Erro ao restaurar configurações');
                    }
                },
                error: () => {
                    this.showStatus('error', 'Erro de conexão');
                },
                complete: () => {
                    $button.prop('disabled', false);
                }
            });
        }

        showStatus(type, message, duration = 3000) {
            const $status = $('#cpr-status');
            
            $status.removeClass('success error')
                   .addClass(type)
                   .text(message)
                   .show();
            
            setTimeout(() => {
                $status.fadeOut();
            }, duration);
        }

        updatePreview() {
            // Função placeholder para preview
            // Pode ser implementada futuramente se necessário
            console.log('Preview atualizado');
        }

        // Preview removido - apenas link simples

        collectSettings() {
            const settings = {};
            
            $('#cpr-settings-form').find('input, select, textarea').each(function() {
                const $field = $(this);
                const name = $field.attr('name');
                
                if (name && name.startsWith('settings[')) {
                    const key = name.replace('settings[', '').replace(']', '');
                    
                    if ($field.is(':checkbox')) {
                        settings[key] = $field.is(':checked');
                    } else {
                        settings[key] = $field.val();
                    }
                }
            });
            
            return settings;
        }

        // Métodos de preview removidos - agora usamos links diretos

        setupKeyboardNavigation() {
            // Navegação por teclado nas abas
            $('.cpr-nav-tabs .nav-tab').on('keydown', (e) => {
                const $tabs = $('.cpr-nav-tabs .nav-tab');
                const $current = $(e.currentTarget);
                const currentIndex = $tabs.index($current);
                
                let newIndex = currentIndex;
                
                switch (e.key) {
                    case 'ArrowLeft':
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                        break;
                    case 'ArrowRight':
                        newIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                        break;
                    case 'Home':
                        newIndex = 0;
                        break;
                    case 'End':
                        newIndex = $tabs.length - 1;
                        break;
                    default:
                        return;
                }
                
                e.preventDefault();
                $tabs.eq(newIndex).focus().click();
            });
        }
    }

    // Inicializar quando o documento estiver pronto
    $(document).ready(() => {
        window.cprAdmin = new CPRAdmin();
    });

})(jQuery);
