<?php
/**
 * Plugin Name: Redirection Manager
 * Description: Plugin para gerenciar redirecionamentos do site com painel administrativo
 * Version: 1.0.0
 * Author: Membero
 */

// Previne acesso direto
if (!defined('ABSPATH')) {
    exit;
}

class RedirectionManager {
    
    private $plugin_url;
    private $plugin_path;
    
    public function __construct() {
        // Como está sendo carregado do tema, usar get_stylesheet_directory_uri
        $this->plugin_url = get_stylesheet_directory_uri() . '/redirection-manager/';
        $this->plugin_path = get_stylesheet_directory() . '/redirection-manager/';
        
        // Incluir migração
        require_once $this->plugin_path . 'migration.php';
        
        // Incluir arquivo de teste apenas em modo debug
        if (defined('WP_DEBUG') && WP_DEBUG) {
            require_once $this->plugin_path . 'test-redirections.php';
        }
        
        add_action('init', [$this, 'init']);
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        add_action('wp_ajax_save_redirection_settings', [$this, 'save_settings_ajax']);
        add_action('wp_ajax_add_exception', [$this, 'add_exception_ajax']);
        add_action('wp_ajax_remove_exception', [$this, 'remove_exception_ajax']);
        add_action('wp_ajax_dismiss_rm_migration_notice', [$this, 'dismiss_migration_notice']);
        

        
        // Hook principal de redirecionamento
        add_action('template_redirect', [$this, 'handle_redirections'], 5);
        
        // Executar migração se necessário
        if (RedirectionMigration::is_migration_needed()) {
            RedirectionMigration::migrate_settings();
        }
    }
    
    public function init() {
        // Inicialização do plugin
        $this->create_default_options();
    }
    
    private function create_default_options() {
        // Configurações padrão
        $defaults = [
            'redirection_enabled' => true,
            'login_page' => 'acessar',
            'register_page' => 'cadastro', 
            'member_area' => 'area-de-membros',
            'settings_page' => 'painel/settings/',
            'exceptions' => ['cursos'],
            'admin_redirections' => [
                'painel' => 'painel/settings/',
                'courses' => 'area-de-membros'
            ]
        ];
        
        foreach ($defaults as $key => $value) {
            if (get_option('rm_' . $key) === false) {
                add_option('rm_' . $key, $value);
            }
        }
    }
    
    public function add_admin_menu() {
        add_options_page(
            'Redirection Manager',
            'Redirecionamentos',
            'manage_options',
            'redirection-manager',
            [$this, 'admin_page']
        );
    }
    
    public function register_settings() {
        $settings = [
            'redirection_enabled',
            'login_page',
            'register_page', 
            'member_area',
            'settings_page',
            'exceptions',
            'admin_redirections'
        ];
        
        foreach ($settings as $setting) {
            register_setting('redirection_manager', 'rm_' . $setting);
        }
    }
    
    public function enqueue_admin_assets($hook) {
        if ($hook !== 'settings_page_redirection-manager') {
            return;
        }
        
        $css_file = $this->plugin_path . 'assets/admin.css';
        $js_file = $this->plugin_path . 'assets/admin.js';
        
        // Verificar se os arquivos existem
        if (file_exists($css_file)) {
            wp_enqueue_style(
                'redirection-manager-admin',
                $this->plugin_url . 'assets/admin.css',
                [],
                filemtime($css_file)
            );
        }
        
        if (file_exists($js_file)) {
            wp_enqueue_script(
                'redirection-manager-admin',
                $this->plugin_url . 'assets/admin.js',
                ['jquery'],
                filemtime($js_file),
                true
            );
            
            wp_localize_script('redirection-manager-admin', 'rm_ajax', [
                'url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('rm_nonce')
            ]);
        }
        
        // CSS inline como fallback
        add_action('admin_head', [$this, 'add_inline_styles']);
    }
    
    public function admin_page() {
        $enabled = get_option('rm_redirection_enabled', true);
        $login_page = get_option('rm_login_page', 'acessar');
        $register_page = get_option('rm_register_page', 'cadastro');
        $member_area = get_option('rm_member_area', 'area-de-membros');
        $settings_page = get_option('rm_settings_page', 'painel/settings/');
        $exceptions = get_option('rm_exceptions', ['cursos']);
        $admin_redirections = get_option('rm_admin_redirections', []);
        
        include $this->plugin_path . 'templates/admin-page.php';
    }
    
    public function save_settings_ajax() {
        check_ajax_referer('rm_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $settings = [
            'redirection_enabled' => isset($_POST['enabled']) ? (bool)$_POST['enabled'] : false,
            'login_page' => sanitize_text_field($_POST['login_page'] ?? 'acessar')
        ];
        
        foreach ($settings as $key => $value) {
            update_option('rm_' . $key, $value);
        }
        
        wp_send_json_success('Configurações salvas com sucesso!');
    }
    
    public function add_exception_ajax() {
        check_ajax_referer('rm_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $page = sanitize_text_field($_POST['page'] ?? '');
        if (empty($page)) {
            wp_send_json_error('Página não pode estar vazia');
        }
        
        $exceptions = get_option('rm_exceptions', []);
        if (!in_array($page, $exceptions)) {
            $exceptions[] = $page;
            update_option('rm_exceptions', $exceptions);
        }
        
        wp_send_json_success($exceptions);
    }
    
    public function remove_exception_ajax() {
        check_ajax_referer('rm_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $page = sanitize_text_field($_POST['page'] ?? '');
        $exceptions = get_option('rm_exceptions', []);
        $exceptions = array_diff($exceptions, [$page]);
        
        update_option('rm_exceptions', array_values($exceptions));
        wp_send_json_success($exceptions);
    }
    
    public function handle_redirections() {
        // Verifica se está no admin ou fazendo AJAX
        if (is_admin() || wp_doing_ajax() || $this->is_elementor_request()) {
            return;
        }
        
        // Verifica se redirecionamentos estão habilitados
        if (!get_option('rm_redirection_enabled', true)) {
            return;
        }
        
        $login_page = get_option('rm_login_page', 'acessar');
        $register_page = get_option('rm_register_page', 'cadastro');
        $member_area = get_option('rm_member_area', 'area-de-membros');
        $settings_page = get_option('rm_settings_page', 'painel/settings/');
        $exceptions = get_option('rm_exceptions', ['cursos']);
        $admin_redirections = get_option('rm_admin_redirections', []);
        
        $current_path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
        
        // Redirecionamentos administrativos
        foreach ($admin_redirections as $from => $to) {
            if ($current_path === $from) {
                wp_redirect(site_url('/' . $to));
                exit();
            }
        }
        
        // Redirecionamento da página /painel
        if ($current_path === 'painel') {
            wp_redirect(site_url('/' . $settings_page));
            exit();
        }
        
        // Redirecionamentos do TutorLMS
        if (is_singular('courses')) {
            wp_redirect(site_url('/' . $member_area));
            exit();
        }
        
        if (function_exists('tutor_utils') && tutor_utils()->is_tutor_dashboard_page()) {
            $dashboard_page = tutor_utils()->get_tutor_dashboard_page_permalink();
            $current_url = tutor_utils()->get_current_url();
            
            if ($dashboard_page === $current_url) {
                wp_redirect(site_url('/' . $settings_page));
                exit();
            }
        }
        
        // Redirecionamentos para usuários logados
        if ((is_page($login_page) || is_page($register_page)) && is_user_logged_in()) {
            wp_redirect(site_url('/' . $member_area));
            exit();
        }
        
        // Redirecionamentos para usuários não logados
        if (!is_user_logged_in()) {
            $current_page = get_post_field('post_name', get_queried_object_id());
            
            // Verifica se a página atual está nas exceções
            if (!in_array($current_page, $exceptions) && 
                !is_page($login_page) && 
                !is_page($register_page)) {
                wp_redirect(site_url('/' . $login_page));
                exit();
            }
        } else {
            // Redirecionamentos para usuários logados
            if ($current_path === 'courses' || $current_path === 'courses/') {
                wp_redirect(site_url('/' . $member_area));
                exit();
            }
        }
    }
    
    private function is_elementor_request() {
        return isset($_GET['elementor-preview']) || 
               isset($_GET['elementor-edit']) || 
               (isset($_POST['action']) && strpos($_POST['action'], 'elementor') !== false);
    }
    

    
    public function dismiss_migration_notice() {
        check_ajax_referer('rm_nonce', 'nonce');
        update_option('rm_migration_notice_dismissed', true);
        wp_send_json_success();
    }
    
    public function add_inline_styles() {
        $css_content = file_get_contents($this->plugin_path . 'assets/admin.css');
        if ($css_content) {
            echo '<style type="text/css">' . $css_content . '</style>';
        }
    }
}

// Hooks de ativação e desativação
register_activation_hook(__FILE__, 'redirection_manager_activate');
register_deactivation_hook(__FILE__, 'redirection_manager_deactivate');

function redirection_manager_activate() {
    // Executar migração na ativação
    require_once plugin_dir_path(__FILE__) . 'migration.php';
    if (RedirectionMigration::is_migration_needed()) {
        RedirectionMigration::migrate_settings();
    }
    
    // Criar opções padrão se não existirem
    $defaults = [
        'rm_redirection_enabled' => true,
        'rm_login_page' => 'acessar',
        'rm_register_page' => 'cadastro',
        'rm_member_area' => 'area-de-membros',
        'rm_settings_page' => 'painel/settings/',
        'rm_exceptions' => ['cursos'],
        'rm_admin_redirections' => [
            'painel' => 'painel/settings/',
            'courses' => 'area-de-membros'
        ]
    ];
    
    foreach ($defaults as $key => $value) {
        if (get_option($key) === false) {
            add_option($key, $value);
        }
    }
}

function redirection_manager_deactivate() {
    // Limpar cache se necessário
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
}

// Inicializa o plugin
new RedirectionManager();