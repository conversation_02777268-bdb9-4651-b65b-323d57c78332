<?php
/**
 * Template para formulário de redefinir senha
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

// Obter configurações
$plugin = new CustomPasswordReset();
$errors = $plugin->get_errors();
$success = $plugin->get_success();
$settings = $plugin->get_settings();

// Verificar se temos os parâmetros necessários
$key = isset($_GET['key']) ? sanitize_text_field($_GET['key']) : '';
$login = isset($_GET['login']) ? sanitize_text_field($_GET['login']) : '';

if (empty($key) || empty($login)) {
    wp_redirect(wp_login_url() . '?action=lostpassword');
    exit;
}

// Verificar se a chave é válida
$user = check_password_reset_key($key, $login);
if (is_wp_error($user)) {
    wp_redirect(wp_login_url() . '?action=lostpassword&error=invalidkey');
    exit;
}

// Obter logo
$logo_url = !empty($settings['logo_url']) ? $settings['logo_url'] : '';
if (empty($logo_url)) {
    $logo_id = get_theme_mod('custom_logo');
    $logo_url = $logo_id ? wp_get_attachment_image_src($logo_id, 'full')[0] : '';
}

// Obter URL de login
$login_url = wp_login_url();
if (function_exists('get_option')) {
    $login_page = get_option('rm_login_page', 'acessar');
    if (!empty($login_page)) {
        $login_url = site_url('/' . $login_page . '/');
    }
}
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="noindex, nofollow">
    <title>Redefinir Senha - <?php bloginfo('name'); ?></title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">

    <!-- Custom Styles Inline -->
    <style>
        <?php
        $css_file = dirname(__FILE__) . '/../assets/css/password-reset.css';
        if (file_exists($css_file)) {
            echo file_get_contents($css_file);
        }
        ?>

        /* Custom CSS Variables Override */
        :root {
            --cpr-primary-bg: <?php echo esc_attr($settings['background_color']); ?> !important;
            --cpr-card-bg: <?php echo esc_attr($settings['card_background']); ?> !important;
            --cpr-text-color: <?php echo esc_attr($settings['text_color']); ?> !important;
            --cpr-button-bg: <?php echo esc_attr($settings['button_color']); ?> !important;
            --cpr-button-text: <?php echo esc_attr($settings['button_text_color']); ?> !important;
        }

        <?php if (!empty($settings['custom_css'])): ?>
        /* Custom CSS */
        <?php echo $settings['custom_css']; ?>
        <?php endif; ?>
    </style>
    
    <?php wp_head(); ?>
</head>
<body class="cpr-password-reset cpr-theme-<?php echo esc_attr($settings['theme']); ?>">
    
    <div class="cpr-container">
        <!-- Card -->
        <div class="cpr-card">
            <!-- Logo dentro do card -->
            <div class="cpr-logo-container">
                <?php if (!empty($logo_url)): ?>
                    <img src="<?php echo esc_url($logo_url); ?>" alt="<?php bloginfo('name'); ?>" class="cpr-logo">
                <?php else: ?>
                    <div class="cpr-logo-icon">
                        <i class="fas fa-key"></i>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Título -->
            <h1 class="cpr-title">Redefinir Senha</h1>
            <p class="cpr-subtitle">Digite sua nova senha abaixo. Certifique-se de que seja segura e fácil de lembrar.</p>
            
            <!-- Mensagens de erro -->
            <?php if (!empty($errors)): ?>
                <?php foreach ($errors as $error): ?>
                    <div class="cpr-message cpr-message-error">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo esc_html($error); ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <!-- Mensagem de sucesso -->
            <?php if (!empty($success)): ?>
                <div class="cpr-message cpr-message-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo esc_html($success); ?>
                </div>
            <?php else: ?>
                <!-- Formulário -->
                <form method="post" class="cpr-form" novalidate>
                    <?php wp_nonce_field('cpr_reset_password', 'cpr_nonce'); ?>
                    <input type="hidden" name="key" value="<?php echo esc_attr($key); ?>">
                    <input type="hidden" name="login" value="<?php echo esc_attr($login); ?>">
                    
                    <div class="cpr-form-group">
                        <label for="pass1" class="cpr-label">
                            Nova senha
                        </label>
                        <input 
                            type="password" 
                            name="pass1" 
                            id="pass1" 
                            class="cpr-input" 
                            placeholder="Digite sua nova senha"
                            required
                            autocomplete="new-password"
                            minlength="6"
                        >
                    </div>
                    
                    <div class="cpr-form-group">
                        <label for="pass2" class="cpr-label">
                            Confirmar nova senha
                        </label>
                        <input 
                            type="password" 
                            name="pass2" 
                            id="pass2" 
                            class="cpr-input" 
                            placeholder="Confirme sua nova senha"
                            required
                            autocomplete="new-password"
                            minlength="6"
                        >
                    </div>
                    
                    <!-- Indicador de força da senha -->
                    <div id="password-strength" class="cpr-password-strength" style="display: none;">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <span class="strength-text"></span>
                    </div>
                    
                    <button type="submit" name="cpr_reset_password_submit" class="cpr-button">
                        <i class="fas fa-save"></i>
                        Redefinir Senha
                    </button>
                </form>
            <?php endif; ?>
            
            <!-- Link para voltar ao login -->
            <div class="cpr-footer">
                <a href="<?php echo esc_url($login_url); ?>" class="cpr-link">
                    <i class="fas fa-arrow-left"></i>
                    Voltar ao Login
                </a>
            </div>
        </div>
    </div>
    
    <!-- CSS adicional para indicador de força da senha -->
    <style>
        .cpr-password-strength {
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .strength-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-text {
            font-size: 12px;
            font-weight: 500;
        }
        
        .strength-weak .strength-fill {
            width: 25%;
            background: #ff6b6b;
        }
        
        .strength-fair .strength-fill {
            width: 50%;
            background: #ffc107;
        }
        
        .strength-good .strength-fill {
            width: 75%;
            background: #28a745;
        }
        
        .strength-strong .strength-fill {
            width: 100%;
            background: #51cf66;
        }
        
        .strength-weak .strength-text { color: #ff6b6b; }
        .strength-fair .strength-text { color: #ffc107; }
        .strength-good .strength-text { color: #28a745; }
        .strength-strong .strength-text { color: #51cf66; }
    </style>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Configurações para o JavaScript
        var cprSettings = {
            theme: '<?php echo esc_js($settings['theme']); ?>',
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('cpr_nonce'); ?>'
        };
        
        // Indicador de força da senha
        $(document).ready(function() {
            $('#pass1').on('input', function() {
                const password = $(this).val();
                const $indicator = $('#password-strength');
                
                if (password.length === 0) {
                    $indicator.hide();
                    return;
                }
                
                $indicator.show();
                
                let strength = 0;
                let strengthText = '';
                let strengthClass = '';
                
                // Calcular força
                if (password.length >= 8) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;
                
                // Determinar nível
                if (strength < 2) {
                    strengthText = 'Fraca';
                    strengthClass = 'strength-weak';
                } else if (strength < 3) {
                    strengthText = 'Razoável';
                    strengthClass = 'strength-fair';
                } else if (strength < 4) {
                    strengthText = 'Boa';
                    strengthClass = 'strength-good';
                } else {
                    strengthText = 'Forte';
                    strengthClass = 'strength-strong';
                }
                
                $indicator.removeClass('strength-weak strength-fair strength-good strength-strong')
                          .addClass(strengthClass);
                $indicator.find('.strength-text').text('Força da senha: ' + strengthText);
            });
        });
    </script>
    <script src="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/js/password-reset.js'; ?>"></script>
    
    <?php wp_footer(); ?>
</body>
</html>
