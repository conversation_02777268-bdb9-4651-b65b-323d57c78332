(function($) {
    var clickLock = false;

    $(document).ready(function(){
        // Manipulação de alternância da barra lateral
        $('.sidebar-toggle').on('click', function(){
            handleSidebarToggle();
        });

        // Manipulação de navegação
        $('.navigation .elementor-icon-list-item').on('click', function(){
            handleNavigationClick($(this));
        });

        // Verificar a navegação ativa ao carregar a página
        setActiveNavigationItemOnLoad();
    });

    $(window).on('load resize', function(){
        handleWindowResize();
    });

    $(window).on('load scroll', function(){
        updateActiveNavigationOnScroll();
    });

    // Clicar fora da área específica para fechar o menu
    $('body').on('click', function(e){
        handleBodyClick(e);
    });

    // Função para manipular a alternância da barra lateral
    function handleSidebarToggle() {
        var $body = $('body');
        var windowWidth = $(window).width();

        if (windowWidth < 768) {
            $body.toggleClass('left');
        } else {
            if ($body.hasClass('open')) {
                $body.removeClass('open');
                setTimeout(function(){
                    $body.removeClass('opening');
                }, 300);
            } else {
                $body.addClass('opening');
                setTimeout(function(){
                    $body.addClass('open');
                }, 300);
            }
        }
    }

    // Função para manipular o clique na navegação
    function handleNavigationClick($element) {
        if (clickLock) return;

        $('.navigation .elementor-icon-list-item').removeClass('active');
        $element.addClass('active');
        clickLock = true;
        setTimeout(function(){
            clickLock = false;
        }, 500);
        $('body').toggleClass('left');
    }

    // Função para ajustar a barra lateral com base no redimensionamento da janela
    function handleWindowResize() {
        var windowWidth = $(window).width();
        var $body = $('body');

        if (windowWidth < 768) {
            $body.removeClass('open opening').addClass('left');
        } else if (windowWidth < 1025) {
            $body.addClass('open opening');
        } else {
            $body.removeClass('open opening');
        }
    }

    // Função para definir o item de navegação ativo ao carregar a página
    function setActiveNavigationItemOnLoad() {
        var currentUrl = location.protocol + '//' + location.host + location.pathname;

        $('.elementor-icon-list-item a').each(function(){
            if ($(this).attr('href') === currentUrl) {
                $(this).parent().addClass('active');
            }
        });
    }

    // Função para atualizar o item de navegação ativo ao rolar a página
    function updateActiveNavigationOnScroll() {
        if (clickLock) return;

        var ids = [];
        $('.elementor-top-section').each(function(){
            if ($(window).scrollTop() + $(window).height() - 350 > $(this).offset().top) {
                ids.push($(this).attr('id'));
            }
        });

        // Remove IDs indefinidos
        ids = ids.filter(function(element) {
            return element !== undefined;
        });

        var id = ids[ids.length - 1];
        $('.elementor-icon-list-item').removeClass('active');
        if (id) {
            $('[href="#' + id + '"]').parent().addClass('active');
        }
    }

    // Função para manipular cliques fora da área do menu
    function handleBodyClick(e) {
        var $target = $(e.target);
        var windowWidth = $(window).width();

        if (!$target.closest('.elementor-location-header').length && !$target.hasClass('elementor-location-header')) {
            if (windowWidth < 768) {
                $('body').addClass('left');
            } else if (windowWidth < 1025) {
                $('body').addClass('open opening');
            }
        }
    }

})(jQuery);
