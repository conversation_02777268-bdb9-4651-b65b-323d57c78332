/* Redirection Manager Ad<PERSON> Styles */
.rm-admin-wrap {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Container */
.rm-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Grid Layout */
.rm-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .rm-grid {
        grid-template-columns: 1fr;
    }
}

/* Cards */
.rm-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid #e1e5e9;
    overflow: hidden;
    transition: all 0.3s ease;
}

.rm-card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.rm-card-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
}

.rm-card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rm-card-header .dashicons {
    color: #667eea;
}

.rm-card-body {
    padding: 1.5rem;
}

.rm-card-desc {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* Status Card */
.rm-status-card {
    grid-column: 1 / -1;
}

.rm-status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rm-status-info strong {
    display: block;
    color: #2c3e50;
    font-size: 1.1rem;
}

.rm-status-desc {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Toggle Switch */
.rm-toggle {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.rm-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.rm-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.rm-toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

.rm-toggle input:checked + .rm-toggle-slider {
    background-color: #667eea;
}

.rm-toggle input:checked + .rm-toggle-slider:before {
    transform: translateX(26px);
}

/* Form Fields */
.rm-field-group {
    margin-bottom: 1.5rem;
}



.rm-field-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.rm-field-group input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.rm-field-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.rm-field-group small {
    display: block;
    margin-top: 0.5rem;
    color: #6c757d;
    font-size: 0.85rem;
}

/* Exceptions */
.rm-add-exception {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.rm-add-exception input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
}

.rm-exceptions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.rm-exception-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.rm-exception-name {
    color: #2c3e50;
    font-weight: 500;
}

.rm-remove-exception {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    margin-left: 0.5rem;
    padding: 0;
    display: flex;
    align-items: center;
}

.rm-remove-exception:hover {
    color: #c82333;
}

/* Redirections */
.rm-redirections-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.rm-redirection-item {
    display: grid;
    grid-template-columns: 1fr auto 1fr auto;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.rm-redirection-from,
.rm-redirection-to {
    display: flex;
    flex-direction: column;
}

.rm-redirection-from label,
.rm-redirection-to label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.rm-redirection-from code,
.rm-redirection-to code {
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #e1e5e9;
    font-family: 'Monaco', 'Consolas', monospace;
}

.rm-redirection-arrow {
    color: #667eea;
    font-size: 1.5rem;
    font-weight: bold;
}

.rm-status-active {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Buttons */
.rm-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.rm-btn-primary {
    background: #667eea;
    color: white;
}

.rm-btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.rm-btn-success {
    background: #28a745;
    color: white;
}

.rm-btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.rm-btn-secondary {
    background: #6c757d;
    color: white;
}

.rm-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Actions */
.rm-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
}

/* Loading */
.rm-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.rm-loading-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.rm-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.rm-notifications {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 10000;
}

.rm-notification {
    background: white;
    border-left: 4px solid #28a745;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

.rm-notification.error {
    border-left-color: #dc3545;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .rm-redirection-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .rm-redirection-arrow {
        transform: rotate(90deg);
    }
    
    .rm-actions {
        flex-direction: column;
        align-items: center;
    }
}