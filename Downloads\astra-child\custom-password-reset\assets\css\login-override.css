/* WordPress Login Override Styles */

/* Hide default WordPress elements */
body.login {
    background: #1a1a1a !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
}

body.login.cpr-theme-light {
    background: #f5f5f5 !important;
}

/* Hide WordPress logo and default styling */
.login h1 a {
    display: none !important;
}

.login h1 {
    display: none !important;
}

/* Override login form container */
.login #loginform,
.login #lostpasswordform,
.login #resetpassform {
    background: #2d2d2d !important;
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    padding: 40px 30px !important;
    margin-top: 0 !important;
}

body.login.cpr-theme-light .login #loginform,
body.login.cpr-theme-light .login #lostpasswordform,
body.login.cpr-theme-light .login #resetpassform {
    background: #ffffff !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Form wrapper */
.login form {
    margin: 0 !important;
}

/* Input fields */
.login input[type="text"],
.login input[type="password"],
.login input[type="email"] {
    background: #3a3a3a !important;
    border: 2px solid #555555 !important;
    border-radius: 8px !important;
    color: #ffffff !important;
    font-size: 16px !important;
    padding: 14px 16px !important;
    width: 100% !important;
    margin-bottom: 16px !important;
    transition: all 0.3s ease !important;
}

body.login.cpr-theme-light .login input[type="text"],
body.login.cpr-theme-light .login input[type="password"],
body.login.cpr-theme-light .login input[type="email"] {
    background: #ffffff !important;
    border: 2px solid #dddddd !important;
    color: #333333 !important;
}

.login input[type="text"]:focus,
.login input[type="password"]:focus,
.login input[type="email"]:focus {
    border-color: #4a90e2 !important;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1) !important;
    outline: none !important;
}

/* Labels */
.login label {
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

body.login.cpr-theme-light .login label {
    color: #333333 !important;
}

/* Submit button */
.login .button-primary {
    background: #4a90e2 !important;
    border: none !important;
    border-radius: 8px !important;
    color: #ffffff !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    padding: 14px 20px !important;
    width: 100% !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-shadow: none !important;
    box-shadow: none !important;
    margin-bottom: 20px !important;
}

.login .button-primary:hover {
    background: #357abd !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3) !important;
}

.login .button-primary:active {
    transform: translateY(0) !important;
}

/* Links */
.login #nav a,
.login #backtoblog a {
    color: #4a90e2 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.login #nav a:hover,
.login #backtoblog a:hover {
    color: #357abd !important;
    text-decoration: underline !important;
}

/* Navigation and back to blog */
.login #nav,
.login #backtoblog {
    text-align: center !important;
    margin: 16px 0 !important;
}

/* Messages */
.login .message,
.login #login_error {
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    margin: 0 0 20px 0 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.login .message {
    background-color: rgba(81, 207, 102, 0.1) !important;
    border: 1px solid #51cf66 !important;
    color: #51cf66 !important;
}

.login #login_error {
    background-color: rgba(255, 107, 107, 0.1) !important;
    border: 1px solid #ff6b6b !important;
    color: #ff6b6b !important;
}

body.login.cpr-theme-light .login .message {
    background-color: rgba(39, 174, 96, 0.1) !important;
    border: 1px solid #27ae60 !important;
    color: #27ae60 !important;
}

body.login.cpr-theme-light .login #login_error {
    background-color: rgba(231, 76, 60, 0.1) !important;
    border: 1px solid #e74c3c !important;
    color: #e74c3c !important;
}

/* Form container */
#login {
    width: 400px !important;
    padding: 20px !important;
    margin: 0 auto !important;
    position: relative !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Logo será adicionada dentro do formulário via CSS */

/* Logo dentro do formulário */
.login form::before {
    content: '';
    display: block;
    width: 60px;
    height: 60px;
    background: #4a90e2;
    border-radius: 12px;
    margin: 0 auto 30px auto;
    position: relative;
}

/* Ícone da logo */
.login form::after {
    content: '\f0e0'; /* Font Awesome email icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: -90px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    color: #ffffff;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
}

/* Título após a logo */
.login #loginform h3,
.login #lostpasswordform h3,
.login #resetpassform h3 {
    content: 'Recuperar Acesso';
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    margin: 0 0 12px 0;
    order: 2;
}

body.login.cpr-theme-light .login #loginform h3,
body.login.cpr-theme-light .login #lostpasswordform h3,
body.login.cpr-theme-light .login #resetpassform h3 {
    color: #333333;
}

/* Adicionar título via JavaScript se não existir */
.login form .cpr-title {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    margin: 0 0 12px 0;
}

body.login.cpr-theme-light .login form .cpr-title {
    color: #333333;
}

/* Subtitle */
.login form::after {
    content: 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.';
    display: block;
    font-size: 14px;
    color: #b0b0b0;
    text-align: center;
    margin-bottom: 30px;
    line-height: 1.5;
}

body.login.cpr-theme-light .login form::after {
    color: #666666;
}

/* Theme toggle button */
body.login::before {
    content: '\f186'; /* Font Awesome moon icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2d2d2d;
    border: 2px solid #555555;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    color: #ffffff;
    font-size: 20px;
    z-index: 1000;
}

body.login.cpr-theme-light::before {
    content: '\f185'; /* Font Awesome sun icon */
    background: #ffffff;
    border: 2px solid #dddddd;
    color: #333333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Responsive design */
@media (max-width: 480px) {
    #login {
        width: calc(100% - 30px) !important;
        padding: 15px !important;
    }
    
    .login #loginform,
    .login #lostpasswordform,
    .login #resetpassform {
        padding: 30px 20px !important;
    }
    
    .login form::before {
        font-size: 22px !important;
    }
    
    .login form::after {
        font-size: 13px !important;
    }
    
    body.login::before {
        top: 15px !important;
        right: 15px !important;
        width: 45px !important;
        height: 45px !important;
    }
}

/* Animation */
#login {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-50%) translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateY(0);
    }
}

/* Focus styles for accessibility */
.login input[type="text"]:focus-visible,
.login input[type="password"]:focus-visible,
.login input[type="email"]:focus-visible,
.login .button-primary:focus-visible {
    outline: 2px solid #4a90e2 !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .login input[type="text"],
    .login input[type="password"],
    .login input[type="email"] {
        border-color: #000000 !important;
    }
    
    body.login.cpr-theme-light .login input[type="text"],
    body.login.cpr-theme-light .login input[type="password"],
    body.login.cpr-theme-light .login input[type="email"] {
        border-color: #000000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .login .button-primary:hover {
        transform: none !important;
    }
}
