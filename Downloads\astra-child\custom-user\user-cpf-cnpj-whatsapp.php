<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class UserCustomFields {
    private static $instance = null;
    
    private function __construct() {
        $this->init_hooks();
    }
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function init_hooks() {
        // Filtros e ações para campos personalizados
        add_filter('user_contactmethods', [$this, 'add_contact_methods'], 10, 2);
        add_action('show_user_profile', [$this, 'add_profile_fields']);
        add_action('edit_user_profile', [$this, 'add_profile_fields']);
        add_action('register_form', [$this, 'add_registration_fields']);
        add_action('user_register', [$this, 'save_registration_fields']);
        
        // Validação de campos
        add_filter('registration_errors', [$this, 'validate_registration_fields'], 10, 3);
        add_action('personal_options_update', [$this, 'validate_profile_fields']);
        add_action('edit_user_profile_update', [$this, 'validate_profile_fields']);
    }
    
    /**
     * Sanitiza e valida CPF/CNPJ
     */
    private function sanitize_document($document) {
        // Remove tudo exceto números
        $document = preg_replace('/[^0-9]/', '', $document);
        
        if (strlen($document) !== 11 && strlen($document) !== 14) {
            return false;
        }
        
        return $document;
    }
    
    /**
     * Sanitiza e valida WhatsApp
     */
    private function sanitize_whatsapp($whatsapp) {
        // Remove tudo exceto números e +
        $whatsapp = preg_replace('/[^0-9+]/', '', $whatsapp);
        
        if (strlen($whatsapp) < 10 || strlen($whatsapp) > 15) {
            return false;
        }
        
        return $whatsapp;
    }
    
    /**
     * Adiciona campos de contato
     */
    public function add_contact_methods($methods, $user) {
        $methods['cpf_cnpj'] = __('CPF/CNPJ', 'astra-child');
        $methods['whatsapp'] = __('WhatsApp', 'astra-child');
        return $methods;
    }
    
    /**
     * Adiciona campos ao perfil
     */
    public function add_profile_fields($user) {
        wp_nonce_field('custom_fields_nonce', 'custom_fields_nonce');
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Máscara para CPF/CNPJ
            $('#cpf_cnpj').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length <= 11) {
                    value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
                } else {
                    value = value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
                }
                $(this).val(value);
            });

            // Máscara para WhatsApp
            $('#whatsapp').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length > 0) {
                    value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
                }
                $(this).val(value);
            });
        });
        </script>
        <?php
    }
    
    /**
     * Adiciona campos ao registro
     */
    public function add_registration_fields() {
        wp_nonce_field('custom_fields_nonce', 'custom_fields_nonce');
        ?>
        <p>
            <label for="cpf_cnpj"><?php _e("CPF/CNPJ", "astra-child"); ?><br />
                <input type="text" name="cpf_cnpj" id="cpf_cnpj" class="input" size="25" required />
            </label>
        </p>
        <p>
            <label for="whatsapp"><?php _e("WhatsApp", "astra-child"); ?><br />
                <input type="text" name="whatsapp" id="whatsapp" class="input" size="25" required />
            </label>
        </p>
        <?php
    }
    
    /**
     * Valida campos do registro
     */
    public function validate_registration_fields($errors, $sanitized_user_login, $user_email) {
        if (!isset($_POST['custom_fields_nonce']) || 
            !wp_verify_nonce($_POST['custom_fields_nonce'], 'custom_fields_nonce')) {
            $errors->add('nonce_error', __('Erro de validação.', 'astra-child'));
            return $errors;
        }

        if (empty($_POST['cpf_cnpj'])) {
            $errors->add('cpf_cnpj_error', __('CPF/CNPJ é obrigatório.', 'astra-child'));
        } else {
            $document = $this->sanitize_document($_POST['cpf_cnpj']);
            if (!$document) {
                $errors->add('cpf_cnpj_error', __('CPF/CNPJ inválido.', 'astra-child'));
            }
        }

        if (empty($_POST['whatsapp'])) {
            $errors->add('whatsapp_error', __('WhatsApp é obrigatório.', 'astra-child'));
        } else {
            $whatsapp = $this->sanitize_whatsapp($_POST['whatsapp']);
            if (!$whatsapp) {
                $errors->add('whatsapp_error', __('WhatsApp inválido.', 'astra-child'));
            }
        }

        return $errors;
    }
    
    /**
     * Salva campos do registro
     */
    public function save_registration_fields($user_id) {
        if (!isset($_POST['custom_fields_nonce']) || 
            !wp_verify_nonce($_POST['custom_fields_nonce'], 'custom_fields_nonce')) {
            return;
        }

        if (isset($_POST['cpf_cnpj'])) {
            $document = $this->sanitize_document($_POST['cpf_cnpj']);
            if ($document) {
                update_user_meta($user_id, 'cpf_cnpj', $document);
            }
        }

        if (isset($_POST['whatsapp'])) {
            $whatsapp = $this->sanitize_whatsapp($_POST['whatsapp']);
            if ($whatsapp) {
                update_user_meta($user_id, 'whatsapp', $whatsapp);
            }
        }
    }

    /**
     * Valida campos do perfil
     */
    public function validate_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return false;
        }

        if (!isset($_POST['custom_fields_nonce']) || 
            !wp_verify_nonce($_POST['custom_fields_nonce'], 'custom_fields_nonce')) {
            return false;
        }

        return true;
    }
}

// Inicializa a classe
function init_user_custom_fields() {
    UserCustomFields::get_instance();
}
add_action('init', 'init_user_custom_fields');

// Funções auxiliares (mantidas para compatibilidade)
function get_user_cpf_cnpj($user_id = null) {
    if (null === $user_id) {
        $user_id = get_current_user_id();
    }
    return get_user_meta($user_id, 'cpf_cnpj', true);
}

function get_user_whatsapp($user_id = null) {
    if (null === $user_id) {
        $user_id = get_current_user_id();
    }
    return get_user_meta($user_id, 'whatsapp', true);
}

function get_user_custom_fields($user_id = null) {
    if (null === $user_id) {
        $user_id = get_current_user_id();
    }
    return [
        'cpf_cnpj' => get_user_cpf_cnpj($user_id),
        'whatsapp' => get_user_whatsapp($user_id)
    ];
}