<?php
/**
 * Instalação e ativação do Custom New User Email Plugin
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class CustomNewUserEmailInstaller {
    
    public static function activate() {
        // Criar opções padrão
        self::create_default_options();
        
        // Verificar dependências
        self::check_dependencies();
        
        // Limpar cache
        self::clear_cache();
        
        // Log de ativação
        error_log('Custom New User Email Plugin ativado com sucesso');
    }
    
    public static function deactivate() {
        // Limpar cache
        self::clear_cache();
        
        // Log de desativação
        error_log('Custom New User Email Plugin desativado');
    }
    
    public static function uninstall() {
        // Remover opções (opcional - comentado para preservar configurações)
        // self::remove_options();
        
        // Log de desinstalação
        error_log('Custom New User Email Plugin desinstalado');
    }
    
    private static function create_default_options() {
        $defaults = [
            'cnue_theme' => 'dark',
            'cnue_logo_url' => '',
            'cnue_version' => '1.0.0',
            'cnue_installed_date' => current_time('mysql'),
            
            // Configurações de email
            'cnue_email_enabled' => false,
            'cnue_email_subject' => 'Bem-vindo! Seus dados de acesso',
            'cnue_email_from_name' => get_bloginfo('name'),
            'cnue_email_from_email' => get_option('admin_email'),
            'cnue_email_header_color' => '#4a90e2',
            'cnue_email_header_pattern' => 'none',
            'cnue_email_button_color' => '#4a90e2',
            'cnue_email_footer_text' => 'Este é um email automático, não responda.',
            'cnue_email_greeting' => 'Olá',
            'cnue_email_main_text' => 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:',
            'cnue_email_instruction_text' => 'Clique no botão abaixo para fazer login e começar a usar sua conta:',
            'cnue_email_button_text' => 'Fazer Login',
            'cnue_email_credentials_title' => 'Seus dados de acesso:',
            'cnue_email_username_label' => 'Usuário:',
            'cnue_email_password_label' => 'Senha:',
            'cnue_email_login_url_label' => 'Link de acesso:',
            'cnue_email_security_note' => 'Por segurança, recomendamos que você altere sua senha após o primeiro login.'
        ];
        
        foreach ($defaults as $key => $value) {
            if (get_option($key) === false) {
                add_option($key, $value);
            }
        }
    }
    
    private static function check_dependencies() {
        // Verificar versão do WordPress
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die('Este plugin requer WordPress 5.0 ou superior.');
        }
        
        // Verificar versão do PHP
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die('Este plugin requer PHP 7.4 ou superior.');
        }
        
        // Verificar se jQuery está disponível
        if (!wp_script_is('jquery', 'registered')) {
            wp_register_script('jquery', 'https://code.jquery.com/jquery-3.6.0.min.js');
        }
    }
    
    private static function clear_cache() {
        // Limpar cache do WordPress
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Limpar cache de plugins populares
        
        // WP Rocket
        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
        }
        
        // W3 Total Cache
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
        }
        
        // WP Super Cache
        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
        }
        
        // LiteSpeed Cache
        if (class_exists('LiteSpeed_Cache_API')) {
            LiteSpeed_Cache_API::purge_all();
        }
        
        // SG Optimizer
        if (function_exists('sg_cachepress_purge_cache')) {
            sg_cachepress_purge_cache();
        }
    }
    
    private static function remove_options() {
        $options = [
            'cnue_theme', 'cnue_logo_url', 'cnue_version', 'cnue_installed_date',
            'cnue_email_enabled', 'cnue_email_subject', 'cnue_email_from_name', 'cnue_email_from_email',
            'cnue_email_header_color', 'cnue_email_header_pattern', 'cnue_email_button_color', 'cnue_email_footer_text',
            'cnue_email_greeting', 'cnue_email_main_text', 'cnue_email_instruction_text', 'cnue_email_button_text',
            'cnue_email_credentials_title', 'cnue_email_username_label', 'cnue_email_password_label', 
            'cnue_email_login_url_label', 'cnue_email_security_note'
        ];
        
        foreach ($options as $option) {
            delete_option($option);
        }
    }
    
    public static function upgrade() {
        $current_version = get_option('cnue_version', '0.0.0');
        $new_version = '1.0.0';
        
        if (version_compare($current_version, $new_version, '<')) {
            // Executar atualizações necessárias
            self::run_upgrades($current_version, $new_version);
            
            // Atualizar versão
            update_option('cnue_version', $new_version);
            
            // Log de upgrade
            error_log("Custom New User Email Plugin atualizado de {$current_version} para {$new_version}");
        }
    }
    
    private static function run_upgrades($from_version, $to_version) {
        // Aqui você pode adicionar lógica específica de upgrade
        // Por exemplo:
        
        // if (version_compare($from_version, '1.1.0', '<')) {
        //     // Upgrade para versão 1.1.0
        // }
    }
    
    public static function check_requirements() {
        $requirements = [
            'wp_version' => '5.0',
            'php_version' => '7.4',
            'extensions' => ['json', 'mbstring']
        ];
        
        $errors = [];
        
        // Verificar WordPress
        if (version_compare(get_bloginfo('version'), $requirements['wp_version'], '<')) {
            $errors[] = sprintf('WordPress %s ou superior é necessário.', $requirements['wp_version']);
        }
        
        // Verificar PHP
        if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
            $errors[] = sprintf('PHP %s ou superior é necessário.', $requirements['php_version']);
        }
        
        // Verificar extensões
        foreach ($requirements['extensions'] as $extension) {
            if (!extension_loaded($extension)) {
                $errors[] = sprintf('Extensão PHP %s é necessária.', $extension);
            }
        }
        
        return $errors;
    }
}

// Hooks de ativação/desativação (se usado como plugin independente)
if (defined('ABSPATH') && !defined('WP_UNINSTALL_PLUGIN')) {
    register_activation_hook(__FILE__, ['CustomNewUserEmailInstaller', 'activate']);
    register_deactivation_hook(__FILE__, ['CustomNewUserEmailInstaller', 'deactivate']);
    
    // Hook para verificar upgrades
    add_action('plugins_loaded', ['CustomNewUserEmailInstaller', 'upgrade']);
}
