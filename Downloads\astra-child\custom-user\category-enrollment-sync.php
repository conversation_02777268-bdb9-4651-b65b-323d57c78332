<?php
if (!defined('ABSPATH')) exit;

class TutorCategoryEnrollmentSync {
    private function is_feature_enabled() {
    return get_option('tutor_category_sync_enabled', false);
}

    private static $instance = null;
    private $default_batch_size = 50;
    private $log_enabled = true;
    private $transient_timeout = HOUR_IN_SECONDS;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('save_post_courses', array($this, 'handle_course_update'), 10, 3);
        add_action('set_object_terms', array($this, 'handle_category_change'), 10, 6);
        add_action('tutor_category_sync_event', array($this, 'sync_category_enrollments'), 10, 2);
        add_action('admin_init', array($this, 'register_sync_status_option'));
    }

    protected function log($message) {
        if ($this->log_enabled) {
            error_log('[TutorSync] ' . $message);
        }
    }

    public function register_sync_status_option() {
        register_setting('tutor_sync_options', 'tutor_category_sync_status');
    }

    protected function get_course_students($course_id) {
        global $wpdb;
        $cache_key = 'tutor_course_students_' . $course_id;
        $students = get_transient($cache_key);
        
        if ($students === false) {
            $students = $wpdb->get_col($wpdb->prepare(
                "SELECT DISTINCT post_author 
                FROM {$wpdb->posts} 
                WHERE post_type = 'tutor_enrolled' 
                AND post_parent = %d 
                AND post_status = 'completed'",
                $course_id
            ));
            set_transient($cache_key, $students, $this->transient_timeout);
        }
        
        return $students;
    }

    protected function get_category_courses($category_id) {
        $cache_key = 'tutor_category_courses_' . $category_id;
        $courses = get_transient($cache_key);
        
        if ($courses === false) {
            $courses = get_posts(array(
                'post_type' => 'courses',
                'numberposts' => -1,
                'tax_query' => array(
                    array(
                        'taxonomy' => 'course-category',
                        'field' => 'term_id',
                        'terms' => $category_id
                    )
                ),
                'fields' => 'ids',
                'post_status' => 'publish'
            ));
            set_transient($cache_key, $courses, $this->transient_timeout);
        }
        
        return $courses;
    }

    protected function process_enrollment_batch($students, $course_id) {
        global $wpdb;
        $success_count = 0;
        $error_count = 0;

        $wpdb->query('START TRANSACTION');
        
        try {
            foreach ($students as $student_id) {
                if (!tutils()->is_enrolled($course_id, $student_id)) {
                    $result = tutils()->do_enroll($course_id, 0, $student_id);
                    if ($result) {
                        $success_count++;
                    } else {
                        $error_count++;
                        $this->log("Erro ao matricular usuário {$student_id} no curso {$course_id}");
                    }
                }
                
                if (connection_status() !== CONNECTION_NORMAL) {
                    $wpdb->query('ROLLBACK');
                    $this->log("Timeout durante processamento do lote");
                    return false;
                }
            }
            
            $wpdb->query('COMMIT');
            $this->log("Lote processado: {$success_count} matrículas realizadas, {$error_count} erros");
            return true;
            
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            $this->log("Erro durante processamento do lote: " . $e->getMessage());
            return false;
        }
    }

    public function sync_category_enrollments($course_id, $category_id) {
        if (!$this->is_feature_enabled()) return;
        
        $this->log("Iniciando sincronização para curso {$course_id} na categoria {$category_id}");
        
        update_option('tutor_category_sync_status', array(
            'status' => 'running',
            'course_id' => $course_id,
            'category_id' => $category_id,
            'start_time' => current_time('mysql')
        ));

        $category_courses = $this->get_category_courses($category_id);
        
        if (empty($category_courses)) {
            $this->log("Nenhum curso encontrado na categoria {$category_id}");
            return;
        }

        $all_students = array();
        foreach ($category_courses as $cat_course_id) {
            if ($cat_course_id === $course_id) continue;
            
            $course_students = $this->get_course_students($cat_course_id);
            $all_students = array_merge($all_students, $course_students);
        }
        
        $all_students = array_unique($all_students);
        
        if (empty($all_students)) {
            $this->log("Nenhum aluno para sincronizar");
            return;
        }

        $batch_size = $this->adjust_batch_size();
        $batches = array_chunk($all_students, $batch_size);
        $total_batches = count($batches);
        $processed_batches = 0;

        foreach ($batches as $batch) {
            $processed_batches++;
            $this->log("Processando lote {$processed_batches} de {$total_batches}");
            
            if (!$this->process_enrollment_batch($batch, $course_id)) {
                $this->schedule_retry($course_id, $category_id);
                return;
            }
        }

        update_option('tutor_category_sync_status', array(
            'status' => 'completed',
            'course_id' => $course_id,
            'category_id' => $category_id,
            'end_time' => current_time('mysql')
        ));
        
        $this->log("Sincronização concluída para curso {$course_id}");
    }

    protected function adjust_batch_size() {
        $server_memory = ini_get('memory_limit');
        $server_memory = intval($server_memory);

        if ($server_memory <= 128) {
            return 25; // Servidores básicos
        } elseif ($server_memory <= 512) {
            return 50; // Servidores médios
        } else {
            return 100; // Servidores robustos
        }
    }

    protected function schedule_retry($course_id, $category_id) {
        $args = array(
            'course_id' => $course_id,
            'category_id' => $category_id
        );
        
        wp_schedule_single_event(time() + 300, 'tutor_category_sync_event', $args);
        $this->log("Agendada nova tentativa de sincronização em 5 minutos");
    }

        public function handle_course_update($post_id, $post, $update) {
            if (!$this->is_feature_enabled()) return;
        
            if ($post->post_type !== 'courses' || $post->post_status !== 'publish') {
                return;
            }
        
            $categories = wp_get_post_terms($post_id, 'course-category', array('fields' => 'ids'));
        
            if (!empty($categories)) {
                foreach ($categories as $category_id) {
                    wp_schedule_single_event(time() + 30, 'tutor_category_sync_event', array(
                        'course_id' => $post_id,
                        'category_id' => $category_id
                    ));
                    $this->log("Sincronização agendada para curso {$post_id} na categoria {$category_id}");
                }
            }
        }

public function handle_category_change($object_id, $terms, $tt_ids, $taxonomy, $append, $old_tt_ids) {
    if (!$this->is_feature_enabled()) return;
    
    if ($taxonomy !== 'course-category') return;

    $post = get_post($object_id);
    if ($post->post_type !== 'courses' || $post->post_status !== 'publish') return;

    $new_categories = array_diff($tt_ids, $old_tt_ids);
    foreach ($new_categories as $category_id) {
        if (!wp_next_scheduled('tutor_category_sync_event', array('course_id' => $object_id, 'category_id' => $category_id))) {
            wp_schedule_single_event(time() + 30, 'tutor_category_sync_event', array(
                'course_id' => $object_id,
                'category_id' => $category_id
            ));
            $this->log("Sincronização agendada após mudança de categoria para curso {$object_id}, categoria {$category_id}");
        } else {
            $this->log("Sincronização já agendada para curso {$object_id}, categoria {$category_id}");
        }
    }
}

}

add_action('init', array('TutorCategoryEnrollmentSync', 'get_instance'));

function tutor_sync_settings_field() {
    $is_enabled = get_option('tutor_category_sync_enabled', false);
    ?>
    <tr>
        <th scope="row">Ativar Inscrição Automática</th>
        <td>
            <label class="protecao-toggle-switch">
                <input type="checkbox" name="tutor_category_sync_enabled" value="1" <?php checked($is_enabled, true); ?>>
                <span class="protecao-toggle-slider"></span>
            </label>
           <p class="description">Habilite esta opção para garantir que alunos matriculados em qualquer curso de uma categoria sejam automaticamente inscritos em outros cursos da mesma categoria. Essa funcionalidade facilita o gerenciamento de matrículas em categorias relacionadas.</p>
        </td>
    </tr>
    <?php
}

function tutor_sync_register_settings() {
    register_setting('general', 'tutor_category_sync_enabled', array(
        'type' => 'boolean',
        'description' => 'Ativar ou desativar a sincronização de categorias',
        'sanitize_callback' => 'rest_sanitize_boolean',
        'default' => false,
    ));

    add_settings_field(
        'tutor_category_sync_enabled',
        'Sincronização Automática de Matrículas por Categoria',
        'tutor_sync_settings_field',
        'general',
        'default'
    );
}
add_action('admin_init', 'tutor_sync_register_settings');
