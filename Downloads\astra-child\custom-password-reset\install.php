<?php
/**
 * Instalação e ativação do Custom Password Reset Plugin
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

class CustomPasswordResetInstaller {
    
    public static function activate() {
        // Criar opções padrão
        self::create_default_options();
        
        // Verificar dependências
        self::check_dependencies();
        
        // Criar páginas necessárias (opcional)
        self::create_pages();
        
        // Limpar cache
        self::clear_cache();
        
        // Log de ativação
        error_log('Custom Password Reset Plugin ativado com sucesso');
    }
    
    public static function deactivate() {
        // Limpar cache
        self::clear_cache();
        
        // Log de desativação
        error_log('Custom Password Reset Plugin desativado');
    }
    
    public static function uninstall() {
        // Remover opções (opcional - comentado para preservar configurações)
        // self::remove_options();
        
        // Remover páginas criadas (opcional)
        // self::remove_pages();
        
        // Log de desinstalação
        error_log('Custom Password Reset Plugin desinstalado');
    }
    
    private static function create_default_options() {
        $defaults = [
            'cpr_theme' => 'dark',
            'cpr_logo_url' => '',
            'cpr_background_color' => '#1a1a1a',
            'cpr_card_background' => '#2d2d2d',
            'cpr_text_color' => '#ffffff',
            'cpr_button_color' => '#4a90e2',
            'cpr_button_text_color' => '#ffffff',
            'cpr_title' => 'Recuperar Acesso',
            'cpr_subtitle' => 'Digite seu e-mail corporativo para receber as instruções de redefinição de senha.',
            'cpr_button_text' => 'Enviar Instruções',
            'cpr_back_to_login_text' => 'Voltar ao Login',
            'cpr_custom_page_slug' => '',
            'cpr_enable_custom_page' => false,
            'cpr_custom_css' => '',
            'cpr_custom_js' => '',
            'cpr_version' => '1.0.0',
            'cpr_installed_date' => current_time('mysql')
        ];
        
        foreach ($defaults as $key => $value) {
            if (get_option($key) === false) {
                add_option($key, $value);
            }
        }
    }
    
    private static function check_dependencies() {
        // Verificar versão do WordPress
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die('Este plugin requer WordPress 5.0 ou superior.');
        }
        
        // Verificar versão do PHP
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die('Este plugin requer PHP 7.4 ou superior.');
        }
        
        // Verificar se jQuery está disponível
        if (!wp_script_is('jquery', 'registered')) {
            wp_register_script('jquery', 'https://code.jquery.com/jquery-3.6.0.min.js');
        }
    }
    
    private static function create_pages() {
        // Criar página de esqueceu senha (opcional)
        $page_slug = 'esqueceu-senha';
        $page_title = 'Esqueceu a Senha';
        
        if (!get_page_by_path($page_slug)) {
            $page_data = [
                'post_title' => $page_title,
                'post_name' => $page_slug,
                'post_content' => '[custom_password_reset type="lost_password"]',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1,
                'comment_status' => 'closed',
                'ping_status' => 'closed'
            ];
            
            $page_id = wp_insert_post($page_data);
            
            if ($page_id) {
                update_option('cpr_lost_password_page_id', $page_id);
            }
        }
        
        // Criar página de redefinir senha (opcional)
        $reset_page_slug = 'redefinir-senha';
        $reset_page_title = 'Redefinir Senha';
        
        if (!get_page_by_path($reset_page_slug)) {
            $reset_page_data = [
                'post_title' => $reset_page_title,
                'post_name' => $reset_page_slug,
                'post_content' => '[custom_password_reset type="reset_password"]',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1,
                'comment_status' => 'closed',
                'ping_status' => 'closed'
            ];
            
            $reset_page_id = wp_insert_post($reset_page_data);
            
            if ($reset_page_id) {
                update_option('cpr_reset_password_page_id', $reset_page_id);
            }
        }
    }
    
    private static function clear_cache() {
        // Limpar cache do WordPress
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Limpar cache de plugins populares
        
        // WP Rocket
        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
        }
        
        // W3 Total Cache
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
        }
        
        // WP Super Cache
        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
        }
        
        // LiteSpeed Cache
        if (class_exists('LiteSpeed_Cache_API')) {
            LiteSpeed_Cache_API::purge_all();
        }
        
        // SG Optimizer
        if (function_exists('sg_cachepress_purge_cache')) {
            sg_cachepress_purge_cache();
        }
    }
    
    private static function remove_options() {
        $options = [
            'cpr_theme', 'cpr_logo_url', 'cpr_background_color', 'cpr_card_background',
            'cpr_text_color', 'cpr_button_color', 'cpr_button_text_color',
            'cpr_title', 'cpr_subtitle', 'cpr_button_text', 'cpr_back_to_login_text',
            'cpr_custom_page_slug', 'cpr_enable_custom_page', 'cpr_custom_css', 'cpr_custom_js',
            'cpr_version', 'cpr_installed_date', 'cpr_lost_password_page_id', 'cpr_reset_password_page_id'
        ];
        
        foreach ($options as $option) {
            delete_option($option);
        }
    }
    
    private static function remove_pages() {
        // Remover páginas criadas
        $page_ids = [
            get_option('cpr_lost_password_page_id'),
            get_option('cpr_reset_password_page_id')
        ];
        
        foreach ($page_ids as $page_id) {
            if ($page_id) {
                wp_delete_post($page_id, true);
            }
        }
    }
    
    public static function upgrade() {
        $current_version = get_option('cpr_version', '0.0.0');
        $new_version = '1.0.0';
        
        if (version_compare($current_version, $new_version, '<')) {
            // Executar atualizações necessárias
            self::run_upgrades($current_version, $new_version);
            
            // Atualizar versão
            update_option('cpr_version', $new_version);
            
            // Log de upgrade
            error_log("Custom Password Reset Plugin atualizado de {$current_version} para {$new_version}");
        }
    }
    
    private static function run_upgrades($from_version, $to_version) {
        // Aqui você pode adicionar lógica específica de upgrade
        // Por exemplo:
        
        // if (version_compare($from_version, '1.1.0', '<')) {
        //     // Upgrade para versão 1.1.0
        // }
    }
    
    public static function check_requirements() {
        $requirements = [
            'wp_version' => '5.0',
            'php_version' => '7.4',
            'extensions' => ['json', 'mbstring']
        ];
        
        $errors = [];
        
        // Verificar WordPress
        if (version_compare(get_bloginfo('version'), $requirements['wp_version'], '<')) {
            $errors[] = sprintf('WordPress %s ou superior é necessário.', $requirements['wp_version']);
        }
        
        // Verificar PHP
        if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
            $errors[] = sprintf('PHP %s ou superior é necessário.', $requirements['php_version']);
        }
        
        // Verificar extensões
        foreach ($requirements['extensions'] as $extension) {
            if (!extension_loaded($extension)) {
                $errors[] = sprintf('Extensão PHP %s é necessária.', $extension);
            }
        }
        
        return $errors;
    }
}

// Hooks de ativação/desativação (se usado como plugin independente)
if (defined('ABSPATH') && !defined('WP_UNINSTALL_PLUGIN')) {
    register_activation_hook(__FILE__, ['CustomPasswordResetInstaller', 'activate']);
    register_deactivation_hook(__FILE__, ['CustomPasswordResetInstaller', 'deactivate']);
    
    // Hook para verificar upgrades
    add_action('plugins_loaded', ['CustomPasswordResetInstaller', 'upgrade']);
}
