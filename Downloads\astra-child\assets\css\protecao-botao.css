.protecao-toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.protecao-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.protecao-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.protecao-toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .protecao-toggle-slider {
  background-color: #07FABA;
}

input:checked + .protecao-toggle-slider:before {
  transform: translateX(26px);
}
