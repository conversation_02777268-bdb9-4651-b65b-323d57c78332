<?php
/**
 * Template de Email para Redefinição de Senha
 * Template moderno, clean e minimal para emails de redefinição de senha
 */

if (!defined('ABSPATH')) {
    exit;
}

// Obter configurações
$settings = [
    'email_logo_url' => get_option('cpr_email_logo_url', ''),
    'email_header_color' => get_option('cpr_email_header_color', '#4a90e2'),
    'email_header_pattern' => get_option('cpr_email_header_pattern', 'none'),
    'email_icon_color' => get_option('cpr_email_icon_color', '#2d3748'),
    'email_button_color' => get_option('cpr_email_button_color', '#4a90e2'),
    'email_footer_text' => get_option('cpr_email_footer_text', 'Este é um email automático, não responda.'),
    'email_from_name' => get_option('cpr_email_from_name', get_bloginfo('name')),
    'email_greeting' => get_option('cpr_email_greeting', 'Olá'),
    'email_main_text' => get_option('cpr_email_main_text', 'Recebemos uma solicitação para redefinir a senha da sua conta em {site_name}.'),
    'email_instruction_text' => get_option('cpr_email_instruction_text', 'Se você fez esta solicitação, clique no botão abaixo para criar uma nova senha:'),
    'email_button_text' => get_option('cpr_email_button_text', 'Redefinir Minha Senha')
];

$site_name = get_bloginfo('name');
$site_url = get_site_url();

// Função para determinar se uma cor é clara ou escura
function is_light_color($hex) {
    $hex = str_replace('#', '', $hex);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    $brightness = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
    return $brightness > 155;
}

// Determinar cores do texto baseado na cor do cabeçalho
$header_is_light = is_light_color($settings['email_header_color']);
$title_color = $header_is_light ? '#2d3748' : '#ffffff';
$subtitle_color = $header_is_light ? '#718096' : 'rgba(255, 255, 255, 0.8)';

// Substituir placeholders
$settings['email_main_text'] = str_replace('{site_name}', $site_name, $settings['email_main_text']);
$settings['email_signature'] = str_replace('{site_name}', $site_name, $settings['email_signature']);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="x-apple-disable-message-reformatting">
    <title>Redefinir Senha - <?php echo esc_html($site_name); ?></title>
    <style>
        /* Reset CSS */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
        }

        .email-wrapper {
            width: 100%;
            background-color: #f5f5f5;
            padding: 40px 20px;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background-color: <?php echo esc_attr($settings['email_header_color']); ?>;
            padding: 40px 40px 20px 40px;
            text-align: center;
            border-bottom: 2px solid #e5e5e5;
            position: relative;
            overflow: hidden;
            <?php
            $pattern = $settings['email_header_pattern'];
            if ($pattern !== 'none') {
                // Gerar seed aleatório baseado no timestamp + microtime para patterns únicos
                $seed = substr(md5(microtime(true) . $pattern . rand(1000, 9999)), 0, 8);
                srand(hexdec($seed)); // Usar o seed para garantir aleatoriedade consistente
                $pattern_opacity = '0.15';
                $white_opacity = '0.08';

                switch ($pattern) {
                    case 'floating_dots':
                        // Pontos flutuantes com posições aleatórias
                        $positions = [];
                        for ($i = 0; $i < 12; $i++) {
                            $x = rand(5, 95);
                            $y = rand(10, 90);
                            $size = rand(3, 8);
                            $positions[] = "radial-gradient(circle at {$x}% {$y}%, rgba(255,255,255,{$white_opacity}) {$size}px, transparent " . ($size + 2) . "px)";
                        }
                        echo 'background-image: ' . implode(', ', $positions) . ';';
                        break;

                    case 'organic_shapes':
                        // Formas orgânicas abstratas
                        $shapes = [];
                        for ($i = 0; $i < 6; $i++) {
                            $x = rand(0, 100);
                            $y = rand(0, 100);
                            $size = rand(40, 80);
                            $shapes[] = "radial-gradient(ellipse {$size}px " . rand(20, 60) . "px at {$x}% {$y}%, rgba(255,255,255,{$white_opacity}) 0%, transparent 70%)";
                        }
                        echo 'background-image: ' . implode(', ', $shapes) . ';';
                        break;

                    case 'geometric_minimal':
                        // Triângulos e formas geométricas minimalistas
                        echo 'background-image: ';
                        echo 'linear-gradient(135deg, transparent 25%, rgba(255,255,255,' . $white_opacity . ') 25%, rgba(255,255,255,' . $white_opacity . ') 50%, transparent 50%), ';
                        echo 'linear-gradient(45deg, transparent 25%, rgba(255,255,255,' . ($white_opacity * 0.5) . ') 25%, rgba(255,255,255,' . ($white_opacity * 0.5) . ') 50%, transparent 50%);';
                        echo 'background-size: ' . rand(60, 120) . 'px ' . rand(60, 120) . 'px, ' . rand(40, 80) . 'px ' . rand(40, 80) . 'px;';
                        echo 'background-position: ' . rand(0, 50) . 'px ' . rand(0, 50) . 'px, ' . rand(20, 70) . 'px ' . rand(20, 70) . 'px;';
                        break;

                    case 'flowing_lines':
                        // Linhas fluidas e curvas
                        $curves = [];
                        for ($i = 0; $i < 4; $i++) {
                            $angle = rand(0, 180);
                            $curves[] = "linear-gradient({$angle}deg, transparent 40%, rgba(255,255,255,{$white_opacity}) 50%, transparent 60%)";
                        }
                        echo 'background-image: ' . implode(', ', $curves) . ';';
                        echo 'background-size: ' . rand(100, 200) . 'px ' . rand(20, 40) . 'px;';
                        break;

                    case 'scattered_elements':
                        // Elementos espalhados aleatoriamente
                        echo 'background-image: ';
                        $elements = [];
                        for ($i = 0; $i < 8; $i++) {
                            $x = rand(10, 90);
                            $y = rand(15, 85);
                            $size = rand(2, 6);
                            $elements[] = "radial-gradient(circle at {$x}% {$y}%, rgba(255,255,255,{$white_opacity}) {$size}px, transparent " . ($size + 1) . "px)";
                        }
                        // Adicionar algumas linhas sutis
                        for ($i = 0; $i < 3; $i++) {
                            $angle = rand(0, 180);
                            $elements[] = "linear-gradient({$angle}deg, transparent 48%, rgba(255,255,255," . ($white_opacity * 0.5) . ") 50%, transparent 52%)";
                        }
                        echo implode(', ', $elements) . ';';
                        break;

                    case 'modern_grid':
                        // Grade moderna com elementos aleatórios
                        echo 'background-image: ';
                        echo 'linear-gradient(rgba(255,255,255,' . ($white_opacity * 0.3) . ') 1px, transparent 1px), ';
                        echo 'linear-gradient(90deg, rgba(255,255,255,' . ($white_opacity * 0.3) . ') 1px, transparent 1px);';
                        echo 'background-size: ' . rand(30, 60) . 'px ' . rand(30, 60) . 'px;';
                        // Adicionar pontos de destaque aleatórios
                        $highlights = [];
                        for ($i = 0; $i < 5; $i++) {
                            $x = rand(10, 90);
                            $y = rand(20, 80);
                            $highlights[] = "radial-gradient(circle at {$x}% {$y}%, rgba(255,255,255,{$white_opacity}) 3px, transparent 4px)";
                        }
                        echo ', ' . implode(', ', $highlights) . ';';
                        break;

                    case 'abstract_art':
                        // Arte abstrata com múltiplas camadas
                        echo 'background-image: ';
                        $layers = [];

                        // Camada 1: Formas grandes
                        for ($i = 0; $i < 3; $i++) {
                            $x = rand(0, 100);
                            $y = rand(0, 100);
                            $size1 = rand(80, 150);
                            $size2 = rand(40, 100);
                            $layers[] = "radial-gradient(ellipse {$size1}px {$size2}px at {$x}% {$y}%, rgba(255,255,255," . ($white_opacity * 0.4) . ") 0%, transparent 60%)";
                        }

                        // Camada 2: Linhas curvas
                        for ($i = 0; $i < 4; $i++) {
                            $angle = rand(0, 180);
                            $layers[] = "linear-gradient({$angle}deg, transparent 30%, rgba(255,255,255,{$white_opacity}) 50%, transparent 70%)";
                        }

                        // Camada 3: Pontos de destaque
                        for ($i = 0; $i < 6; $i++) {
                            $x = rand(15, 85);
                            $y = rand(20, 80);
                            $size = rand(4, 12);
                            $layers[] = "radial-gradient(circle at {$x}% {$y}%, rgba(255,255,255," . ($white_opacity * 1.5) . ") {$size}px, transparent " . ($size + 2) . "px)";
                        }

                        echo implode(', ', $layers) . ';';
                        break;
                }
            }
            ?>
        }



        .lock-icon {
            width: 60px;
            height: 60px;
            background-color: <?php echo esc_attr($settings['email_icon_color']); ?>;
            border-radius: 50%;
            margin: 0 auto 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }



        .email-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: <?php echo esc_attr($title_color); ?>;
        }

        .email-header .subtitle {
            font-size: 14px;
            color: <?php echo esc_attr($subtitle_color); ?>;
            margin-bottom: 0;
        }

        .email-body {
            padding: 40px;
        }

        .greeting {
            font-size: 16px;
            margin-bottom: 25px;
            color: #2d3748;
            border-left: 4px solid #e5e5e5;
            padding-left: 15px;
        }

        .message {
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .message .highlight {
            color: #3182ce;
            font-weight: 500;
        }

        .instruction-text {
            font-size: 15px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #4a5568;
        }

        .button-container {
            text-align: left;
      
        }

        .reset-button {
            display: inline-block;
            background-color: <?php echo esc_attr($settings['email_button_color']); ?>;
            color: white !important;
            text-decoration: none;
            padding: 15px 35px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
            text-align: center;
            letter-spacing: 0.5px;
        }

        .expiry-notice {
            font-size: 13px;
            color: #718096;
            text-align: left;
            margin-top: 15px;
        }



        .signature {
            margin-top: 40px;
            padding-top: 25px;
            border-top: 1px solid #e2e8f0;
        }



        .signature-text {
            text-align: center;
            font-size: 14px;
            color: #2d3748;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .signature-subtitle {
            text-align: center;
            font-size: 12px;
            color: #718096;
        }

        .email-footer {
            background-color: #f7fafc;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .footer-dots {
            margin-bottom: 15px;
        }

        .footer-dots::before {
            content: "• • •";
            color: #cbd5e0;
            font-size: 16px;
        }

        .footer-text {
            font-size: 12px;
            color: #718096;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .footer-site {
            font-size: 12px;
            color: #4a5568;
            font-weight: 500;
        }

        .footer-email {
            font-size: 11px;
            color: #a0aec0;
            margin-top: 5px;
        }

        /* Responsivo */
        @media only screen and (max-width: 600px) {
            .email-wrapper {
                padding: 20px 10px;
            }

            .email-header,
            .email-body,
            .email-footer {
                padding: 30px 25px !important;
            }

            .email-header h1 {
                font-size: 22px !important;
            }

            .reset-button {
                display: block !important;
                width: 100% !important;
                padding: 15px !important;
            }

            .expiry-notice {
                text-align: center !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">

                <h1>Redefinir Senha</h1>
                <p class="subtitle">Solicitação de redefinição de senha</p>
            </div>

            <!-- Body -->
            <div class="email-body">
                <!-- Greeting -->
                <div class="greeting">
                    <?php
                    // Pegar o primeiro nome do usuário
                    $first_name = get_user_meta($user_data->ID, 'first_name', true);
                    if (empty($first_name)) {
                        $first_name = $user_data->display_name;
                    }
                    if (empty($first_name) || $first_name === 'eu') {
                        $first_name = $user_data->user_login;
                    }

                    // Garantir que a primeira letra de cada palavra seja maiúscula
                    $first_name = ucwords(strtolower($first_name));

                    echo esc_html($settings['email_greeting']) . ', ' . esc_html($first_name) . '!';
                    ?>
                </div>

                <!-- Main Message -->
                <div class="message">
                    <?php echo wp_kses_post($settings['email_main_text']); ?>
                </div>

                <div class="instruction-text">
                    <?php echo esc_html($settings['email_instruction_text']); ?>
                </div>

                <!-- CTA Button -->
                <div class="button-container">
                    <a href="<?php echo esc_url($reset_url); ?>" class="reset-button" style="background-color: <?php echo esc_attr($settings['email_button_color']); ?> !important;">
                        <?php echo esc_html($settings['email_button_text']); ?>
                    </a>
                    <div class="expiry-notice">
                        Este link expira em 24 horas
                    </div>
                </div>




            </div>

            <!-- Footer -->
            <div class="email-footer">
                <div class="footer-dots"></div>
                <div class="footer-text">
                    <?php echo esc_html($settings['email_footer_text']); ?>
                </div>
                <div class="footer-site">
                    <?php echo esc_html($settings['email_from_name']); ?>
                </div>
                <div class="footer-email">
                    Este email foi enviado para <?php echo esc_html($user_data->user_email); ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>