<?php
/**
 * Template para exibição do termo no frontend
 */
if (!defined('ABSPATH')) exit;
?>
<div class="termo-uso-overlay">
    <div class="termo-uso-container">
      <div class="termo-uso-header">
        <h2>Termo de Uso</h2>
        <div class="termo-uso-alert">
            <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
            <p>Por favor, leia o termo até o final para habilitar o botão de aceite.</p>
        </div>
    </div>
        <div class="termo-uso-conteudo">
            <?php echo do_shortcode(wpautop($options['texto_termo'])); ?>
        </div>
        
        <div class="termo-uso-info">
            <div class="termo-info-grid">
                <div class="termo-info-item">
                    <strong>IP:</strong> <?php echo esc_html($ip); ?>
                </div>
                <div class="termo-info-item">
                    <strong>Data e Hora:</strong> <?php echo esc_html($data); ?>
                </div>
            </div>
            <div class="termo-info-browser">
                <strong>Navegador:</strong> <?php echo esc_html($navegador); ?>
            </div>
        </div>
        
        <div class="termo-uso-botoes">
            <button id="aceitar-termo" class="termo-btn termo-btn-primary disabled">
                <?php echo esc_html($options['texto_botao_aceitar'] ?? 'Sim, eu aceito'); ?>
            </button>
            <button id="sair-termo" class="termo-btn termo-btn-danger">
                <?php echo esc_html($options['texto_botao_sair'] ?? 'Sair'); ?>
            </button>
        </div>
        
        <div class="termo-uso-loading">
            <div class="termo-loading-spinner"></div>
            <span>Processando...</span>
        </div>
        
        <div class="termo-uso-success">
            <div class="termo-success-icon">✓</div>
            <span>Termo aceito com sucesso!</span>
        </div>
    </div>
</div>